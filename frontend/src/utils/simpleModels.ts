import * as THREE from 'three';

/**
 * Simple 3D model generator for testing and fallback purposes
 * Creates basic geometric shapes that can be used as placeholder accessories
 */

export class SimpleModelGenerator {
  private models = new Map<string, THREE.Object3D>();

  /**
   * Create a simple glasses model using Three.js geometry
   */
  createGlassesModel(): THREE.Object3D {
    const group = new THREE.Group();
    
    // Frame material
    const frameMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x333333,
      shininess: 30
    });
    
    // Lens material
    const lensMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x87CEEB,
      transparent: true,
      opacity: 0.3,
      shininess: 100
    });

    // Left lens
    const leftLensGeometry = new THREE.RingGeometry(0.3, 0.4, 16);
    const leftLens = new THREE.Mesh(leftLensGeometry, lensMaterial);
    leftLens.position.set(-0.5, 0, 0);
    group.add(leftLens);

    // Right lens
    const rightLensGeometry = new THREE.RingGeometry(0.3, 0.4, 16);
    const rightLens = new THREE.Mesh(rightLensGeometry, lensMaterial);
    rightLens.position.set(0.5, 0, 0);
    group.add(rightLens);

    // Bridge
    const bridgeGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.2);
    const bridge = new THREE.Mesh(bridgeGeometry, frameMaterial);
    bridge.rotation.z = Math.PI / 2;
    bridge.position.set(0, 0, 0);
    group.add(bridge);

    // Left temple
    const leftTempleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.2);
    const leftTemple = new THREE.Mesh(leftTempleGeometry, frameMaterial);
    leftTemple.rotation.z = Math.PI / 2;
    leftTemple.position.set(-0.9, 0, 0);
    group.add(leftTemple);

    // Right temple
    const rightTempleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.2);
    const rightTemple = new THREE.Mesh(rightTempleGeometry, frameMaterial);
    rightTemple.rotation.z = Math.PI / 2;
    rightTemple.position.set(0.9, 0, 0);
    group.add(rightTemple);

    // Scale and position for face
    group.scale.setScalar(0.8);
    group.position.set(0, 0, 0.1);

    return group;
  }

  /**
   * Create a simple hat model
   */
  createHatModel(): THREE.Object3D {
    const group = new THREE.Group();
    
    const hatMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x8B4513,
      shininess: 10
    });

    // Hat crown
    const crownGeometry = new THREE.CylinderGeometry(0.6, 0.6, 0.4, 16);
    const crown = new THREE.Mesh(crownGeometry, hatMaterial);
    crown.position.set(0, 0.2, 0);
    group.add(crown);

    // Hat brim
    const brimGeometry = new THREE.CylinderGeometry(1.0, 1.0, 0.05, 32);
    const brim = new THREE.Mesh(brimGeometry, hatMaterial);
    brim.position.set(0, 0, 0);
    group.add(brim);

    // Position above head
    group.position.set(0, 0.8, 0);
    group.scale.setScalar(0.7);

    return group;
  }

  /**
   * Create sunglasses model
   */
  createSunglassesModel(): THREE.Object3D {
    const group = new THREE.Group();
    
    // Frame material
    const frameMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x000000,
      shininess: 50
    });
    
    // Dark lens material
    const lensMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x1a1a1a,
      transparent: true,
      opacity: 0.8,
      shininess: 100
    });

    // Left lens
    const leftLensGeometry = new THREE.CircleGeometry(0.35, 16);
    const leftLens = new THREE.Mesh(leftLensGeometry, lensMaterial);
    leftLens.position.set(-0.5, 0, 0);
    group.add(leftLens);

    // Right lens
    const rightLensGeometry = new THREE.CircleGeometry(0.35, 16);
    const rightLens = new THREE.Mesh(rightLensGeometry, lensMaterial);
    rightLens.position.set(0.5, 0, 0);
    group.add(rightLens);

    // Frame rings
    const leftFrameGeometry = new THREE.RingGeometry(0.35, 0.38, 16);
    const leftFrame = new THREE.Mesh(leftFrameGeometry, frameMaterial);
    leftFrame.position.set(-0.5, 0, 0.01);
    group.add(leftFrame);

    const rightFrameGeometry = new THREE.RingGeometry(0.35, 0.38, 16);
    const rightFrame = new THREE.Mesh(rightFrameGeometry, frameMaterial);
    rightFrame.position.set(0.5, 0, 0.01);
    group.add(rightFrame);

    // Bridge
    const bridgeGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.2);
    const bridge = new THREE.Mesh(bridgeGeometry, frameMaterial);
    bridge.rotation.z = Math.PI / 2;
    bridge.position.set(0, 0, 0.01);
    group.add(bridge);

    // Temples
    const leftTempleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.2);
    const leftTemple = new THREE.Mesh(leftTempleGeometry, frameMaterial);
    leftTemple.rotation.z = Math.PI / 2;
    leftTemple.position.set(-0.9, 0, 0.01);
    group.add(leftTemple);

    const rightTempleGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1.2);
    const rightTemple = new THREE.Mesh(rightTempleGeometry, frameMaterial);
    rightTemple.rotation.z = Math.PI / 2;
    rightTemple.position.set(0.9, 0, 0.01);
    group.add(rightTemple);

    group.scale.setScalar(0.8);
    group.position.set(0, 0, 0.1);

    return group;
  }

  /**
   * Create a baseball cap model
   */
  createBaseballCapModel(): THREE.Object3D {
    const group = new THREE.Group();
    
    const capMaterial = new THREE.MeshPhongMaterial({ 
      color: 0x1e40af,
      shininess: 20
    });

    // Cap crown
    const crownGeometry = new THREE.SphereGeometry(0.6, 16, 8, 0, Math.PI * 2, 0, Math.PI / 2);
    const crown = new THREE.Mesh(crownGeometry, capMaterial);
    crown.position.set(0, 0.1, 0);
    group.add(crown);

    // Visor
    const visorGeometry = new THREE.CylinderGeometry(0.8, 0.6, 0.05, 16, 1, false, 0, Math.PI);
    const visor = new THREE.Mesh(visorGeometry, capMaterial);
    visor.position.set(0, -0.1, 0.4);
    visor.rotation.x = -Math.PI / 6;
    group.add(visor);

    // Position above head
    group.position.set(0, 0.7, 0);
    group.scale.setScalar(0.8);

    return group;
  }

  /**
   * Get a model by name
   */
  getModel(modelName: string): THREE.Object3D | null {
    if (this.models.has(modelName)) {
      return this.models.get(modelName)!.clone();
    }
    
    let model: THREE.Object3D | null = null;
    switch (modelName) {
      case 'simple-glasses':
        model = this.createGlassesModel();
        break;
      case 'simple-hat':
        model = this.createHatModel();
        break;
      case 'simple-sunglasses':
        model = this.createSunglassesModel();
        break;
      case 'simple-baseball-cap':
        model = this.createBaseballCapModel();
        break;
      default:
        console.warn(`Unknown model: ${modelName}`);
        return null;
    }
    
    if (model) {
      this.models.set(modelName, model);
      return model.clone();
    }
    
    return null;
  }

  /**
   * Get list of available simple models
   */
  getAvailableModels() {
    return {
      glasses: [
        {
          id: 'simple-glasses',
          name: 'Simple Glasses',
          description: 'Basic geometric glasses for testing',
          model_url: null,
          thumbnail_url: null,
          file_size_mb: 0,
          default_transform: {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: 1
          },
          tags: ['simple', 'test'],
          is_featured: true,
          average_rating: 5,
          category: 'glasses',
          category_name: 'Glasses',
          category_slug: 'glasses',
          quality: 'low' as const,
          polygon_count: 100,
          download_count: 0,
          usage_count: 0,
          created_at: new Date().toISOString()
        },
        {
          id: 'simple-sunglasses',
          name: 'Simple Sunglasses',
          description: 'Basic geometric sunglasses for testing',
          model_url: null,
          thumbnail_url: null,
          file_size_mb: 0,
          default_transform: {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: 1
          },
          tags: ['simple', 'test', 'sunglasses'],
          is_featured: true,
          average_rating: 5,
          category: 'glasses',
          category_name: 'Glasses',
          category_slug: 'glasses',
          quality: 'low' as const,
          polygon_count: 120,
          download_count: 0,
          usage_count: 0,
          created_at: new Date().toISOString()
        }
      ],
      hats: [
        {
          id: 'simple-hat',
          name: 'Simple Hat',
          description: 'Basic geometric hat for testing',
          model_url: null,
          thumbnail_url: null,
          file_size_mb: 0,
          default_transform: {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: 1
          },
          tags: ['simple', 'test'],
          is_featured: true,
          average_rating: 5,
          category: 'hats',
          category_name: 'Hats',
          category_slug: 'hats',
          quality: 'low' as const,
          polygon_count: 80,
          download_count: 0,
          usage_count: 0,
          created_at: new Date().toISOString()
        },
        {
          id: 'simple-baseball-cap',
          name: 'Simple Baseball Cap',
          description: 'Basic geometric baseball cap for testing',
          model_url: null,
          thumbnail_url: null,
          file_size_mb: 0,
          default_transform: {
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: 1
          },
          tags: ['simple', 'test', 'baseball'],
          is_featured: true,
          average_rating: 5,
          category: 'hats',
          category_name: 'Hats',
          category_slug: 'hats',
          quality: 'low' as const,
          polygon_count: 90,
          download_count: 0,
          usage_count: 0,
          created_at: new Date().toISOString()
        }
      ]
    };
  }
}

export const simpleModelGenerator = new SimpleModelGenerator();
