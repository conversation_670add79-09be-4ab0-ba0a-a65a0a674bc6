import { useState, useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';

export type WebXRSessionMode = 'immersive-ar' | 'immersive-vr' | 'inline';
export type WebXRReferenceSpace = 'viewer' | 'local' | 'local-floor' | 'bounded-floor' | 'unbounded';

interface WebXRCapabilities {
  supportsAR: boolean;
  supportsVR: boolean;
  supportsInline: boolean;
  supportedFeatures: string[];
}

interface WebXRSessionState {
  isSupported: boolean;
  isSessionActive: boolean;
  sessionMode: WebXRSessionMode | null;
  referenceSpace: XRReferenceSpace | null;
  session: XRSession | null;
  error: string | null;
  capabilities: WebXRCapabilities;
}

interface UseWebXROptions {
  requiredFeatures?: string[];
  optionalFeatures?: string[];
  preferredMode?: WebXRSessionMode;
  enableHandTracking?: boolean;
  enableFaceTracking?: boolean;
}

interface UseWebXRReturn extends WebXRSessionState {
  startSession: (mode: WebXRSessionMode) => Promise<void>;
  endSession: () => Promise<void>;
  checkSupport: () => Promise<WebXRCapabilities>;
  getControllers: () => THREE.Group[];
  getHands: () => THREE.Group[];
  onSessionStart: (callback: (session: XRSession) => void) => void;
  onSessionEnd: (callback: () => void) => void;
  onControllerConnected: (callback: (controller: THREE.Group) => void) => void;
  onControllerDisconnected: (callback: (controller: THREE.Group) => void) => void;
}

export const useWebXR = (options: UseWebXROptions = {}): UseWebXRReturn => {
  const {
    requiredFeatures = [],
    optionalFeatures = ['hand-tracking', 'face-tracking'],
    preferredMode = 'immersive-ar',
    enableHandTracking = true,
    enableFaceTracking = true
  } = options;

  const [sessionState, setSessionState] = useState<WebXRSessionState>({
    isSupported: false,
    isSessionActive: false,
    sessionMode: null,
    referenceSpace: null,
    session: null,
    error: null,
    capabilities: {
      supportsAR: false,
      supportsVR: false,
      supportsInline: false,
      supportedFeatures: []
    }
  });

  const controllersRef = useRef<THREE.Group[]>([]);
  const handsRef = useRef<THREE.Group[]>([]);
  const sessionCallbacksRef = useRef<{
    onStart: ((session: XRSession) => void)[];
    onEnd: (() => void)[];
    onControllerConnected: ((controller: THREE.Group) => void)[];
    onControllerDisconnected: ((controller: THREE.Group) => void)[];
  }>({
    onStart: [],
    onEnd: [],
    onControllerConnected: [],
    onControllerDisconnected: []
  });

  // Check WebXR support on mount
  useEffect(() => {
    checkSupport();
  }, []);

  const checkSupport = useCallback(async (): Promise<WebXRCapabilities> => {
    try {
      if (!navigator.xr) {
        setSessionState(prev => ({
          ...prev,
          isSupported: false,
          error: 'WebXR not supported in this browser'
        }));
        return {
          supportsAR: false,
          supportsVR: false,
          supportsInline: false,
          supportedFeatures: []
        };
      }

      // Check support for different session modes
      const [arSupported, vrSupported, inlineSupported] = await Promise.all([
        navigator.xr.isSessionSupported('immersive-ar').catch(() => false),
        navigator.xr.isSessionSupported('immersive-vr').catch(() => false),
        navigator.xr.isSessionSupported('inline').catch(() => false)
      ]);

      const capabilities: WebXRCapabilities = {
        supportsAR: arSupported,
        supportsVR: vrSupported,
        supportsInline: inlineSupported,
        supportedFeatures: []
      };

      // Check for optional features
      const featuresToCheck = [
        'hand-tracking',
        'face-tracking',
        'hit-test',
        'dom-overlay',
        'light-estimation',
        'anchors',
        'plane-detection'
      ];

      for (const feature of featuresToCheck) {
        try {
          // Try to create a session with this feature to test support
          if (arSupported) {
            await navigator.xr.requestSession('immersive-ar', {
              optionalFeatures: [feature]
            }).then(session => {
              capabilities.supportedFeatures.push(feature);
              session.end();
            }).catch(() => {
              // Feature not supported
            });
          }
        } catch (error) {
          // Feature not supported
        }
      }

      setSessionState(prev => ({
        ...prev,
        isSupported: arSupported || vrSupported || inlineSupported,
        capabilities,
        error: null
      }));

      return capabilities;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown WebXR error';
      setSessionState(prev => ({
        ...prev,
        isSupported: false,
        error: errorMessage
      }));
      
      return {
        supportsAR: false,
        supportsVR: false,
        supportsInline: false,
        supportedFeatures: []
      };
    }
  }, []);

  const startSession = useCallback(async (mode: WebXRSessionMode): Promise<void> => {
    try {
      if (!navigator.xr) {
        throw new Error('WebXR not supported');
      }

      if (sessionState.isSessionActive) {
        throw new Error('WebXR session already active');
      }

      // Prepare session options
      const sessionOptions: XRSessionInit = {
        requiredFeatures: [...requiredFeatures],
        optionalFeatures: [...optionalFeatures]
      };

      // Add hand tracking if enabled and supported
      if (enableHandTracking && sessionState.capabilities.supportedFeatures.includes('hand-tracking')) {
        sessionOptions.optionalFeatures?.push('hand-tracking');
      }

      // Add face tracking if enabled and supported
      if (enableFaceTracking && sessionState.capabilities.supportedFeatures.includes('face-tracking')) {
        sessionOptions.optionalFeatures?.push('face-tracking');
      }

      // Request WebXR session
      const session = await navigator.xr.requestSession(mode, sessionOptions);

      // Get reference space
      const referenceSpace = await session.requestReferenceSpace('local');

      setSessionState(prev => ({
        ...prev,
        isSessionActive: true,
        sessionMode: mode,
        session,
        referenceSpace,
        error: null
      }));

      // Set up session event listeners
      session.addEventListener('end', handleSessionEnd);

      // Notify callbacks
      sessionCallbacksRef.current.onStart.forEach(callback => callback(session));

      console.log(`WebXR ${mode} session started successfully`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start WebXR session';
      setSessionState(prev => ({
        ...prev,
        error: errorMessage
      }));
      throw error;
    }
  }, [sessionState.isSessionActive, sessionState.capabilities, requiredFeatures, optionalFeatures, enableHandTracking, enableFaceTracking]);

  const endSession = useCallback(async (): Promise<void> => {
    try {
      if (sessionState.session) {
        await sessionState.session.end();
      }
    } catch (error) {
      console.error('Error ending WebXR session:', error);
    }
  }, [sessionState.session]);

  const handleSessionEnd = useCallback(() => {
    setSessionState(prev => ({
      ...prev,
      isSessionActive: false,
      sessionMode: null,
      session: null,
      referenceSpace: null
    }));

    // Clear controllers and hands
    controllersRef.current = [];
    handsRef.current = [];

    // Notify callbacks
    sessionCallbacksRef.current.onEnd.forEach(callback => callback());

    console.log('WebXR session ended');
  }, []);

  // Callback registration functions
  const onSessionStart = useCallback((callback: (session: XRSession) => void) => {
    sessionCallbacksRef.current.onStart.push(callback);
  }, []);

  const onSessionEnd = useCallback((callback: () => void) => {
    sessionCallbacksRef.current.onEnd.push(callback);
  }, []);

  const onControllerConnected = useCallback((callback: (controller: THREE.Group) => void) => {
    sessionCallbacksRef.current.onControllerConnected.push(callback);
  }, []);

  const onControllerDisconnected = useCallback((callback: (controller: THREE.Group) => void) => {
    sessionCallbacksRef.current.onControllerDisconnected.push(callback);
  }, []);

  const getControllers = useCallback((): THREE.Group[] => {
    return [...controllersRef.current];
  }, []);

  const getHands = useCallback((): THREE.Group[] => {
    return [...handsRef.current];
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (sessionState.session) {
        sessionState.session.end().catch(console.error);
      }
    };
  }, [sessionState.session]);

  return {
    ...sessionState,
    startSession,
    endSession,
    checkSupport,
    getControllers,
    getHands,
    onSessionStart,
    onSessionEnd,
    onControllerConnected,
    onControllerDisconnected
  };
};
