import { useEffect, useRef, useState, useCallback } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { VRButton } from 'three/examples/jsm/webxr/VRButton.js';
import { ARButton } from 'three/examples/jsm/webxr/ARButton.js';
import { XRControllerModelFactory } from 'three/examples/jsm/webxr/XRControllerModelFactory.js';
import { XRHandModelFactory } from 'three/examples/jsm/webxr/XRHandModelFactory.js';
import { AccessoryModel, FaceLandmarks, ModelTransform } from '@types/index';
import { simpleModelGenerator } from '@utils/simpleModels';

interface UseThreeJSOptions {
  enableShadows?: boolean;
  enableAntialiasing?: boolean;
  backgroundColor?: number;
  cameraFov?: number;
  enableWebXR?: boolean;
  webXRMode?: 'AR' | 'VR' | 'both';
}

interface UseThreeJSReturn {
  containerRef: React.RefObject<HTMLDivElement>;
  scene: THREE.Scene | null;
  camera: THREE.PerspectiveCamera | null;
  renderer: THREE.WebGLRenderer | null;
  isInitialized: boolean;
  loadedModels: Map<string, THREE.Object3D>;
  loadModel: (model: AccessoryModel) => Promise<THREE.Object3D>;
  removeModel: (modelId: string) => void;
  updateModelTransform: (modelId: string, landmarks: FaceLandmarks) => void;
  setModelVisibility: (modelId: string, visible: boolean) => void;
  captureScreenshot: () => string | null;
  resize: () => void;
  // WebXR specific
  isWebXRSupported: boolean;
  isWebXRActive: boolean;
  controllers: THREE.Group[];
  hands: THREE.Group[];
  enableWebXR: () => void;
  disableWebXR: () => void;
  getWebXRButton: (mode: 'AR' | 'VR') => HTMLElement | null;
}

export const useThreeJS = (options: UseThreeJSOptions = {}): UseThreeJSReturn => {
  const {
    enableShadows = true,
    enableAntialiasing = true,
    backgroundColor = 0x000000,
    cameraFov = 75,
    enableWebXR = false,
    webXRMode = 'both'
  } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const gltfLoaderRef = useRef<GLTFLoader | null>(null);

  // WebXR specific refs
  const controllerModelFactoryRef = useRef<XRControllerModelFactory | null>(null);
  const handModelFactoryRef = useRef<XRHandModelFactory | null>(null);
  const controllersRef = useRef<THREE.Group[]>([]);
  const handsRef = useRef<THREE.Group[]>([]);
  const webXRButtonsRef = useRef<{ AR?: HTMLElement; VR?: HTMLElement }>({});

  const [isInitialized, setIsInitialized] = useState(false);
  const [loadedModels] = useState(new Map<string, THREE.Object3D>());
  const [isWebXRSupported, setIsWebXRSupported] = useState(false);
  const [isWebXRActive, setIsWebXRActive] = useState(false);

  // Initialize Three.js scene
  const initializeThreeJS = useCallback(() => {
    if (!containerRef.current || isInitialized) return;

    try {
      const container = containerRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;

      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(backgroundColor);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(cameraFov, width / height, 0.1, 1000);
      camera.position.set(0, 0, 5);
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({
        antialias: enableAntialiasing,
        alpha: true,
        preserveDrawingBuffer: true // For screenshots
      });
      renderer.setSize(width, height);
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      
      if (enableShadows) {
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      }
      
      rendererRef.current = renderer;
      container.appendChild(renderer.domElement);

      // Add lighting
      setupLighting(scene);

      // Initialize GLTF loader
      gltfLoaderRef.current = new GLTFLoader();

      // Initialize WebXR if enabled
      if (enableWebXR) {
        initializeWebXR(renderer);
      }

      // Start render loop
      startRenderLoop();

      setIsInitialized(true);
      console.log('Three.js initialized successfully');
    } catch (error) {
      console.error('Three.js initialization error:', error);
    }
  }, [isInitialized, backgroundColor, cameraFov, enableAntialiasing, enableShadows]);

  // Setup lighting for the scene
  const setupLighting = (scene: THREE.Scene) => {
    // Ambient light for overall illumination
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    // Directional light for shadows and definition
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = enableShadows;
    
    if (enableShadows) {
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      directionalLight.shadow.camera.near = 0.5;
      directionalLight.shadow.camera.far = 50;
    }
    
    scene.add(directionalLight);

    // Point light for additional illumination
    const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
    pointLight.position.set(-5, 5, 5);
    scene.add(pointLight);
  };

  // Initialize WebXR functionality
  const initializeWebXR = useCallback((renderer: THREE.WebGLRenderer) => {
    try {
      // Enable WebXR on the renderer
      renderer.xr.enabled = true;

      // Check WebXR support
      if (navigator.xr) {
        setIsWebXRSupported(true);

        // Initialize controller and hand model factories
        controllerModelFactoryRef.current = new XRControllerModelFactory();
        handModelFactoryRef.current = new XRHandModelFactory();

        // Create WebXR buttons
        if (webXRMode === 'AR' || webXRMode === 'both') {
          const arButton = ARButton.createButton(renderer, {
            requiredFeatures: ['hit-test'],
            optionalFeatures: ['dom-overlay', 'light-estimation', 'face-tracking']
          });
          webXRButtonsRef.current.AR = arButton;
        }

        if (webXRMode === 'VR' || webXRMode === 'both') {
          const vrButton = VRButton.createButton(renderer);
          webXRButtonsRef.current.VR = vrButton;
        }

        // Set up WebXR session event listeners
        renderer.xr.addEventListener('sessionstart', () => {
          setIsWebXRActive(true);
          setupWebXRControllers();
          console.log('WebXR session started');
        });

        renderer.xr.addEventListener('sessionend', () => {
          setIsWebXRActive(false);
          cleanupWebXRControllers();
          console.log('WebXR session ended');
        });

        console.log('WebXR initialized successfully');
      } else {
        console.warn('WebXR not supported in this browser');
      }
    } catch (error) {
      console.error('WebXR initialization error:', error);
    }
  }, [webXRMode]);

  // Setup WebXR controllers and hands
  const setupWebXRControllers = useCallback(() => {
    if (!rendererRef.current || !sceneRef.current) return;

    const renderer = rendererRef.current;
    const scene = sceneRef.current;

    // Clear existing controllers
    controllersRef.current.forEach(controller => {
      scene.remove(controller);
    });
    handsRef.current.forEach(hand => {
      scene.remove(hand);
    });
    controllersRef.current = [];
    handsRef.current = [];

    // Setup controllers
    for (let i = 0; i < 2; i++) {
      // Controller
      const controller = renderer.xr.getController(i);
      scene.add(controller);
      controllersRef.current.push(controller);

      // Controller grip
      const controllerGrip = renderer.xr.getControllerGrip(i);
      if (controllerModelFactoryRef.current) {
        controllerGrip.add(controllerModelFactoryRef.current.createControllerModel(controllerGrip));
      }
      scene.add(controllerGrip);

      // Hand tracking
      const hand = renderer.xr.getHand(i);
      if (handModelFactoryRef.current) {
        hand.add(handModelFactoryRef.current.createHandModel(hand));
      }
      scene.add(hand);
      handsRef.current.push(hand);
    }
  }, []);

  // Cleanup WebXR controllers
  const cleanupWebXRControllers = useCallback(() => {
    if (!sceneRef.current) return;

    const scene = sceneRef.current;

    controllersRef.current.forEach(controller => {
      scene.remove(controller);
    });
    handsRef.current.forEach(hand => {
      scene.remove(hand);
    });

    controllersRef.current = [];
    handsRef.current = [];
  }, []);

  // Start the render loop
  const startRenderLoop = () => {
    const animate = () => {
      if (rendererRef.current?.xr.isPresenting) {
        // WebXR handles the render loop when in XR mode
        return;
      }

      animationFrameRef.current = requestAnimationFrame(animate);

      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Set up WebXR render loop
    if (rendererRef.current) {
      rendererRef.current.setAnimationLoop((time, frame) => {
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      });
    }

    animate();
  };

  // Load a 3D model
  const loadModel = useCallback(async (model: AccessoryModel): Promise<THREE.Object3D> => {
    return new Promise((resolve, reject) => {
      try {
        let modelObject: THREE.Object3D;

        // Check if this is a simple model (no URL)
        const modelUrl = model.model_url || model.file_url;
        if (!modelUrl && model.id.startsWith('simple-')) {
          const simpleModel = simpleModelGenerator.getModel(model.id);
          if (!simpleModel) {
            reject(new Error(`Simple model not found: ${model.id}`));
            return;
          }
          modelObject = simpleModel;
        } else if (modelUrl && gltfLoaderRef.current) {
          // Load GLTF model
          gltfLoaderRef.current.load(
            modelUrl,
            (gltf) => {
              const loadedModel = gltf.scene;
              processLoadedModel(loadedModel, model);
              resolve(loadedModel);
            },
            (progress) => {
              console.log('Loading progress:', (progress.loaded / progress.total) * 100, '%');
            },
            (error) => {
              console.error('Model loading error:', error);
              reject(error);
            }
          );
          return; // Exit early for async GLTF loading
        } else {
          reject(new Error('No model URL provided and GLTF loader not available'));
          return;
        }

        // Process the model (for simple models)
        processLoadedModel(modelObject, model);
        resolve(modelObject);
      } catch (error) {
        reject(error);
      }
    });
  }, [enableShadows, loadedModels]);

  // Helper function to process loaded models
  const processLoadedModel = useCallback((modelObject: THREE.Object3D, model: AccessoryModel) => {
    // Apply default transform
    const transform = model.default_transform;
    modelObject.position.set(transform.position.x, transform.position.y, transform.position.z);
    modelObject.rotation.set(transform.rotation.x, transform.rotation.y, transform.rotation.z);
    modelObject.scale.setScalar(transform.scale);

    // Enable shadows if configured
    if (enableShadows) {
      modelObject.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });
    }

    // Store the model
    loadedModels.set(model.id, modelObject);

    // Add to scene
    if (sceneRef.current) {
      sceneRef.current.add(modelObject);
    }

    console.log(`Model loaded: ${model.name}`);
  }, [enableShadows, loadedModels]);

  // Remove a model from the scene
  const removeModel = useCallback((modelId: string) => {
    const model = loadedModels.get(modelId);
    if (model && sceneRef.current) {
      sceneRef.current.remove(model);
      loadedModels.delete(modelId);
      console.log(`Model removed: ${modelId}`);
    }
  }, [loadedModels]);

  // Update model position based on face landmarks
  const updateModelTransform = useCallback((modelId: string, landmarks: FaceLandmarks) => {
    const model = loadedModels.get(modelId);
    if (!model) return;

    // Convert normalized coordinates to 3D space
    // This is a simplified mapping - you may need to adjust based on your coordinate system
    const faceCenter = {
      x: (landmarks.left_eye.x + landmarks.right_eye.x) / 2,
      y: (landmarks.left_eye.y + landmarks.right_eye.y) / 2,
      z: (landmarks.left_eye.z || 0 + landmarks.right_eye.z || 0) / 2
    };

    // Map normalized coordinates (-1 to 1) to 3D space
    model.position.x = (faceCenter.x - 0.5) * 10; // Scale as needed
    model.position.y = -(faceCenter.y - 0.5) * 10; // Invert Y axis
    model.position.z = faceCenter.z * 5;

    // Apply face rotation
    model.rotation.z = landmarks.face_angle * (Math.PI / 180);

    // Scale based on face size
    const faceScale = landmarks.face_width * 2; // Adjust multiplier as needed
    model.scale.setScalar(faceScale);
  }, [loadedModels]);

  // Set model visibility
  const setModelVisibility = useCallback((modelId: string, visible: boolean) => {
    const model = loadedModels.get(modelId);
    if (model) {
      model.visible = visible;
    }
  }, [loadedModels]);

  // Capture screenshot
  const captureScreenshot = useCallback((): string | null => {
    if (!rendererRef.current) return null;

    try {
      return rendererRef.current.domElement.toDataURL('image/png');
    } catch (error) {
      console.error('Screenshot capture error:', error);
      return null;
    }
  }, []);

  // Resize handler
  const resize = useCallback(() => {
    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;

    const width = containerRef.current.clientWidth;
    const height = containerRef.current.clientHeight;

    cameraRef.current.aspect = width / height;
    cameraRef.current.updateProjectionMatrix();
    rendererRef.current.setSize(width, height);
  }, []);

  // Initialize on mount
  useEffect(() => {
    initializeThreeJS();

    // Handle window resize
    const handleResize = () => resize();
    window.addEventListener('resize', handleResize);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
      
      // Dispose of loaded models
      loadedModels.forEach((model) => {
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      });
      loadedModels.clear();
    };
  }, [initializeThreeJS, resize, loadedModels]);

  // WebXR utility functions
  const enableWebXRFunc = useCallback(() => {
    if (rendererRef.current && !enableWebXR) {
      initializeWebXR(rendererRef.current);
    }
  }, [initializeWebXR, enableWebXR]);

  const disableWebXRFunc = useCallback(() => {
    if (rendererRef.current) {
      rendererRef.current.xr.enabled = false;
      setIsWebXRSupported(false);
      setIsWebXRActive(false);
      cleanupWebXRControllers();
    }
  }, [cleanupWebXRControllers]);

  const getWebXRButton = useCallback((mode: 'AR' | 'VR'): HTMLElement | null => {
    return webXRButtonsRef.current[mode] || null;
  }, []);

  return {
    containerRef,
    scene: sceneRef.current,
    camera: cameraRef.current,
    renderer: rendererRef.current,
    isInitialized,
    loadedModels,
    loadModel,
    removeModel,
    updateModelTransform,
    setModelVisibility,
    captureScreenshot,
    resize,
    // WebXR specific
    isWebXRSupported,
    isWebXRActive,
    controllers: controllersRef.current,
    hands: handsRef.current,
    enableWebXR: enableWebXRFunc,
    disableWebXR: disableWebXRFunc,
    getWebXRButton
  };
};
