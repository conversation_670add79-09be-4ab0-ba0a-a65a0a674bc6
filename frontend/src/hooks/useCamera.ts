import { useState, useEffect, useRef, useCallback } from 'react';
import { CameraState } from '@types/index';

interface UseCameraOptions {
  facingMode?: 'user' | 'environment';
  width?: number;
  height?: number;
  autoStart?: boolean;
}

interface UseCameraReturn extends CameraState {
  videoRef: React.RefObject<HTMLVideoElement>;
  startCamera: () => Promise<void>;
  stopCamera: () => void;
  switchCamera: () => Promise<void>;
  takePhoto: () => string | null;
  getAvailableDevices: () => Promise<MediaDeviceInfo[]>;
}

export const useCamera = (options: UseCameraOptions = {}): UseCameraReturn => {
  const {
    facingMode = 'user',
    width = 640,
    height = 480,
    autoStart = false
  } = options;

  const [cameraState, setCameraState] = useState<CameraState>({
    isActive: false,
    stream: null,
    error: null,
    deviceId: undefined
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const [currentFacingMode, setCurrentFacingMode] = useState(facingMode);
  const [availableDevices, setAvailableDevices] = useState<MediaDeviceInfo[]>([]);
  const isStartingRef = useRef<boolean>(false);

  // Get available camera devices
  const getAvailableDevices = useCallback(async (): Promise<MediaDeviceInfo[]> => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      setAvailableDevices(videoDevices);
      return videoDevices;
    } catch (error) {
      console.error('Error getting camera devices:', error);
      return [];
    }
  }, []);

  // Start camera with specified constraints
  const startCamera = useCallback(async (): Promise<void> => {
    // Prevent multiple simultaneous starts
    if (isStartingRef.current || cameraState.isActive) {
      console.log('Camera already starting or active');
      return;
    }

    isStartingRef.current = true;

    try {
      setCameraState(prev => ({ ...prev, error: null }));

      // Check if browser supports getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera access is not supported in this browser');
      }

      // Stop existing stream if any
      if (cameraState.stream) {
        cameraState.stream.getTracks().forEach(track => track.stop());
      }

      // Define media constraints
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: currentFacingMode,
          width: { ideal: width },
          height: { ideal: height },
          frameRate: { ideal: 30 }
        },
        audio: false
      };

      // If a specific device is selected, use it
      if (cameraState.deviceId) {
        constraints.video = {
          ...constraints.video,
          deviceId: { exact: cameraState.deviceId }
        };
      }

      // Get media stream
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Set stream to video element
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        
        // Wait for video to be ready
        await new Promise<void>((resolve, reject) => {
          if (!videoRef.current) {
            reject(new Error('Video element not available'));
            return;
          }

          const video = videoRef.current;
          
          const onLoadedMetadata = () => {
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);
            resolve();
          };
          
          const onError = () => {
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);
            reject(new Error('Failed to load video'));
          };

          video.addEventListener('loadedmetadata', onLoadedMetadata);
          video.addEventListener('error', onError);

          // Try to play video with better error handling
          const playPromise = video.play();
          if (playPromise !== undefined) {
            playPromise.catch((playError) => {
              // Ignore AbortError as it's usually due to rapid component re-renders
              if (playError.name !== 'AbortError') {
                console.error('Video play error:', playError);
                reject(playError);
              }
            });
          }
        });
      }

      // Update state
      setCameraState({
        isActive: true,
        stream,
        error: null,
        deviceId: stream.getVideoTracks()[0]?.getSettings().deviceId
      });

      console.log('Camera started successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start camera';
      console.error('Camera start error:', error);

      setCameraState(prev => ({
        ...prev,
        isActive: false,
        stream: null,
        error: errorMessage
      }));

      throw error;
    } finally {
      isStartingRef.current = false;
    }
  }, [currentFacingMode, width, height, cameraState.stream, cameraState.deviceId]);

  // Stop camera
  const stopCamera = useCallback((): void => {
    if (cameraState.stream) {
      cameraState.stream.getTracks().forEach(track => {
        track.stop();
        console.log('Camera track stopped:', track.label);
      });
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setCameraState({
      isActive: false,
      stream: null,
      error: null,
      deviceId: undefined
    });

    console.log('Camera stopped');
  }, [cameraState.stream]);

  // Switch between front and back camera
  const switchCamera = useCallback(async (): Promise<void> => {
    const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
    setCurrentFacingMode(newFacingMode);
    
    if (cameraState.isActive) {
      stopCamera();
      // Small delay to ensure cleanup
      setTimeout(() => {
        startCamera();
      }, 100);
    }
  }, [currentFacingMode, cameraState.isActive, stopCamera, startCamera]);

  // Take a photo from the current video stream
  const takePhoto = useCallback((): string | null => {
    if (!videoRef.current || !cameraState.isActive) {
      return null;
    }

    try {
      const video = videoRef.current;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // Convert to data URL
      return canvas.toDataURL('image/jpeg', 0.9);
    } catch (error) {
      console.error('Photo capture error:', error);
      return null;
    }
  }, [cameraState.isActive]);

  // Auto-start camera if requested
  useEffect(() => {
    if (autoStart) {
      startCamera().catch(console.error);
    }

    // Get available devices on mount
    getAvailableDevices();

    // Cleanup on unmount
    return () => {
      stopCamera();
    };
  }, [autoStart]); // Only run on mount

  // Handle device changes
  useEffect(() => {
    const handleDeviceChange = () => {
      getAvailableDevices();
    };

    navigator.mediaDevices?.addEventListener('devicechange', handleDeviceChange);
    
    return () => {
      navigator.mediaDevices?.removeEventListener('devicechange', handleDeviceChange);
    };
  }, [getAvailableDevices]);

  return {
    ...cameraState,
    videoRef,
    startCamera,
    stopCamera,
    switchCamera,
    takePhoto,
    getAvailableDevices
  };
};
