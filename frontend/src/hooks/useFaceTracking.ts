import { useState, useEffect, useRef, useCallback } from 'react';
import { FaceLandmarks, PerformanceStats } from '@types/index';
import { apiService } from '@services/api';

interface UseFaceTrackingOptions {
  enableClientSide?: boolean;
  enableServerSide?: boolean;
  detectionInterval?: number;
  confidenceThreshold?: number;
}

interface UseFaceTrackingReturn {
  landmarks: FaceLandmarks | null;
  isTracking: boolean;
  error: string | null;
  performance: PerformanceStats;
  startTracking: (videoElement: HTMLVideoElement) => void;
  stopTracking: () => void;
  captureFrame: () => Promise<void>;
}

export const useFaceTracking = (options: UseFaceTrackingOptions = {}): UseFaceTrackingReturn => {
  const {
    enableClientSide = true,
    enableServerSide = true,
    detectionInterval = 100,
    confidenceThreshold = 0.7
  } = options;

  const [landmarks, setLandmarks] = useState<FaceLandmarks | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [performance, setPerformance] = useState<PerformanceStats>({
    djangoAvgTime: 0,
    clientAvgTime: 0,
    djangoCount: 0,
    clientCount: 0,
    fps: 0,
    lastUpdateTime: 0
  });

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const serverIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const faceMeshRef = useRef<any>(null);
  const lastFrameTimeRef = useRef<number>(0);
  const frameCountRef = useRef<number>(0);
  const isStartingRef = useRef<boolean>(false);

  // Initialize MediaPipe Face Mesh for client-side detection
  useEffect(() => {
    if (!enableClientSide) return;

    const initializeMediaPipe = async () => {
      try {
        // For now, disable client-side MediaPipe due to import issues
        // This will fall back to server-side detection
        console.log('Client-side MediaPipe disabled - using server-side detection');

        // TODO: Fix MediaPipe imports in future update
        // const { FaceMesh } = await import('@mediapipe/face_mesh');
        // faceMeshRef.current = new FaceMesh({...});
      } catch (err) {
        console.error('Failed to initialize MediaPipe:', err);
        setError('Client-side face tracking unavailable');
      }
    };

    initializeMediaPipe();
  }, [enableClientSide, confidenceThreshold]);

  const processClientSideResults = useCallback((results: any) => {
    const startTime = performance.now();
    
    if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
      const faceLandmarks = results.multiFaceLandmarks[0];
      
      // Extract key landmarks (MediaPipe uses normalized coordinates)
      const landmarks: FaceLandmarks = {
        left_eye: {
          x: faceLandmarks[33].x,
          y: faceLandmarks[33].y,
          z: faceLandmarks[33].z
        },
        right_eye: {
          x: faceLandmarks[263].x,
          y: faceLandmarks[263].y,
          z: faceLandmarks[263].z
        },
        nose_tip: {
          x: faceLandmarks[1].x,
          y: faceLandmarks[1].y,
          z: faceLandmarks[1].z
        },
        forehead: {
          x: faceLandmarks[10].x,
          y: faceLandmarks[10].y,
          z: faceLandmarks[10].z
        },
        chin: {
          x: faceLandmarks[175].x,
          y: faceLandmarks[175].y,
          z: faceLandmarks[175].z
        },
        face_width: Math.abs(faceLandmarks[454].x - faceLandmarks[234].x),
        face_height: Math.abs(faceLandmarks[10].y - faceLandmarks[175].y),
        face_angle: calculateFaceAngle(faceLandmarks),
        confidence_score: 0.9 // MediaPipe doesn't provide confidence per detection
      };

      setLandmarks(landmarks);
      setError(null);
      
      // Update performance stats
      const processingTime = performance.now() - startTime;
      setPerformance(prev => ({
        ...prev,
        clientAvgTime: (prev.clientAvgTime * prev.clientCount + processingTime) / (prev.clientCount + 1),
        clientCount: prev.clientCount + 1
      }));
    } else {
      setLandmarks(null);
    }

    updateFPS();
  }, []);

  const calculateFaceAngle = (landmarks: any[]): number => {
    // Calculate face rotation based on eye positions
    const leftEye = landmarks[33];
    const rightEye = landmarks[263];
    const deltaY = rightEye.y - leftEye.y;
    const deltaX = rightEye.x - leftEye.x;
    return Math.atan2(deltaY, deltaX) * (180 / Math.PI);
  };

  const updateFPS = useCallback(() => {
    const now = performance.now();
    frameCountRef.current++;
    
    if (now - lastFrameTimeRef.current >= 1000) {
      const fps = frameCountRef.current;
      setPerformance(prev => ({ ...prev, fps, lastUpdateTime: now }));
      frameCountRef.current = 0;
      lastFrameTimeRef.current = now;
    }
  }, []);

  const detectFaceServerSide = useCallback(async (videoElement: HTMLVideoElement) => {
    if (!enableServerSide) return;

    try {
      // Validate video element
      if (!videoElement || videoElement.readyState < 2) {
        console.log('Video not ready for capture');
        return;
      }

      if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
        console.log('Video dimensions not available');
        return;
      }

      const startTime = performance.now();

      // Capture frame from video
      const canvas = document.createElement('canvas');
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Failed to get canvas context');
        return;
      }
      
      ctx.drawImage(videoElement, 0, 0);
      
      // Convert to blob with better error handling
      const blob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/jpeg', 0.8);
      });

      // Validate blob before sending
      if (!blob || blob.size === 0) {
        console.error('Invalid blob created from canvas');
        return;
      }

      console.log(`Sending ${blob.size} byte image to face detection API`);

      // Send to Django backend
      const result = await apiService.detectFace(blob);
      
      if (result.success && result.landmarks) {
        setLandmarks(result.landmarks);
        setError(null);
        
        // Update performance stats
        const processingTime = performance.now() - startTime;
        setPerformance(prev => ({
          ...prev,
          djangoAvgTime: (prev.djangoAvgTime * prev.djangoCount + processingTime) / (prev.djangoCount + 1),
          djangoCount: prev.djangoCount + 1
        }));
      } else {
        setLandmarks(null);
      }
    } catch (err) {
      console.error('Server-side face detection error:', err);
      setError('Face detection failed');
    }
  }, [enableServerSide]);

  const startTracking = useCallback((videoElement: HTMLVideoElement) => {
    if (isTracking || isStartingRef.current) {
      console.log('Face tracking already active or starting');
      return;
    }

    isStartingRef.current = true;
    videoRef.current = videoElement;
    setIsTracking(true);
    setError(null);
    
    // Start client-side tracking if enabled
    if (enableClientSide && faceMeshRef.current) {
      const processFrame = async () => {
        if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
          await faceMeshRef.current.send({ image: videoElement });
        }
      };
      
      intervalRef.current = setInterval(processFrame, detectionInterval);
    }
    
    // Start server-side tracking if enabled (less frequent)
    if (enableServerSide) {
      serverIntervalRef.current = setInterval(() => {
        if (videoElement.readyState === videoElement.HAVE_ENOUGH_DATA) {
          detectFaceServerSide(videoElement);
        }
      }, detectionInterval * 3); // 3x slower than client-side
    }

    // Reset the starting flag
    isStartingRef.current = false;
    console.log('Face tracking started');
  }, [isTracking, enableClientSide, enableServerSide, detectionInterval, detectFaceServerSide]);

  const stopTracking = useCallback(() => {
    setIsTracking(false);
    setLandmarks(null);
    isStartingRef.current = false;

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (serverIntervalRef.current) {
      clearInterval(serverIntervalRef.current);
      serverIntervalRef.current = null;
    }

    console.log('Face tracking stopped');
  }, []);

  const captureFrame = useCallback(async () => {
    if (!videoRef.current) return;
    
    try {
      await detectFaceServerSide(videoRef.current);
    } catch (err) {
      console.error('Frame capture error:', err);
      setError('Failed to capture frame');
    }
  }, [detectFaceServerSide]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTracking();
    };
  }, [stopTracking]);

  return {
    landmarks,
    isTracking,
    error,
    performance,
    startTracking,
    stopTracking,
    captureFrame
  };
};
