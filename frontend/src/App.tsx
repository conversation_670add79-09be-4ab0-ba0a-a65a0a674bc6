import React from 'react';
import { Routes, Route } from 'react-router-dom';
import styled from 'styled-components';
import HomePage from '@components/pages/HomePage';
import TryOnPage from '@components/pages/TryOnPage';
import WebXRTryOnPage from '@components/pages/WebXRTryOnPage';
import Navigation from '@components/layout/Navigation';
import ErrorBoundary from '@components/common/ErrorBoundary';

const AppContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

function App() {
  return (
    <ErrorBoundary>
      <AppContainer>
        <Navigation />
        <MainContent>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/tryron" element={<TryOnPage />} />
            <Route path="/webxr" element={<WebXRTryOnPage />} />
          </Routes>
        </MainContent>
      </AppContainer>
    </ErrorBoundary>
  );
}

export default App;
