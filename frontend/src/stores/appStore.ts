import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { AccessoryModel, AccessoryCategory, FaceLandmarks, PerformanceStats } from '@types/index';

interface AppState {
  // UI State
  isLoading: boolean;
  error: string | null;
  currentView: 'home' | 'tryron';
  
  // Camera State
  isCameraActive: boolean;
  cameraError: string | null;
  
  // Face Tracking State
  isTracking: boolean;
  currentLandmarks: FaceLandmarks | null;
  trackingError: string | null;
  performanceStats: PerformanceStats;
  
  // Models State
  categories: AccessoryCategory[];
  modelsByCategory: Record<string, AccessoryModel[]>;
  selectedCategory: string;
  selectedModel: AccessoryModel | null;
  loadedModels: Set<string>;
  
  // AR State
  isARActive: boolean;
  modelVisibility: Record<string, boolean>;
  
  // Photo State
  capturedPhotos: string[];
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentView: (view: 'home' | 'tryron') => void;
  
  // Camera Actions
  setCameraActive: (active: boolean) => void;
  setCameraError: (error: string | null) => void;
  
  // Face Tracking Actions
  setTracking: (tracking: boolean) => void;
  setCurrentLandmarks: (landmarks: FaceLandmarks | null) => void;
  setTrackingError: (error: string | null) => void;
  updatePerformanceStats: (stats: Partial<PerformanceStats>) => void;
  
  // Models Actions
  setCategories: (categories: AccessoryCategory[]) => void;
  setModelsByCategory: (models: Record<string, AccessoryModel[]>) => void;
  setSelectedCategory: (category: string) => void;
  setSelectedModel: (model: AccessoryModel | null) => void;
  addLoadedModel: (modelId: string) => void;
  removeLoadedModel: (modelId: string) => void;
  
  // AR Actions
  setARActive: (active: boolean) => void;
  setModelVisibility: (modelId: string, visible: boolean) => void;
  toggleModelVisibility: (modelId: string) => void;
  
  // Photo Actions
  addCapturedPhoto: (photoData: string) => void;
  removeCapturedPhoto: (index: number) => void;
  clearCapturedPhotos: () => void;
  
  // Utility Actions
  reset: () => void;
}

const initialPerformanceStats: PerformanceStats = {
  djangoAvgTime: 0,
  clientAvgTime: 0,
  djangoCount: 0,
  clientCount: 0,
  fps: 0,
  lastUpdateTime: 0
};

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Initial State
      isLoading: false,
      error: null,
      currentView: 'home',
      
      isCameraActive: false,
      cameraError: null,
      
      isTracking: false,
      currentLandmarks: null,
      trackingError: null,
      performanceStats: initialPerformanceStats,
      
      categories: [],
      modelsByCategory: {},
      selectedCategory: 'glasses',
      selectedModel: null,
      loadedModels: new Set(),
      
      isARActive: false,
      modelVisibility: {},
      
      capturedPhotos: [],
      
      // UI Actions
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      setCurrentView: (view) => set({ currentView: view }),
      
      // Camera Actions
      setCameraActive: (active) => set({ isCameraActive: active }),
      setCameraError: (error) => set({ cameraError: error }),
      
      // Face Tracking Actions
      setTracking: (tracking) => set({ isTracking: tracking }),
      setCurrentLandmarks: (landmarks) => set({ currentLandmarks: landmarks }),
      setTrackingError: (error) => set({ trackingError: error }),
      updatePerformanceStats: (stats) => 
        set((state) => ({
          performanceStats: { ...state.performanceStats, ...stats }
        })),
      
      // Models Actions
      setCategories: (categories) => set({ categories }),
      setModelsByCategory: (models) => set({ modelsByCategory: models }),
      setSelectedCategory: (category) => set({ selectedCategory: category }),
      setSelectedModel: (model) => set({ selectedModel: model }),
      addLoadedModel: (modelId) => 
        set((state) => ({
          loadedModels: new Set([...state.loadedModels, modelId])
        })),
      removeLoadedModel: (modelId) => 
        set((state) => {
          const newLoadedModels = new Set(state.loadedModels);
          newLoadedModels.delete(modelId);
          return { loadedModels: newLoadedModels };
        }),
      
      // AR Actions
      setARActive: (active) => set({ isARActive: active }),
      setModelVisibility: (modelId, visible) => 
        set((state) => ({
          modelVisibility: { ...state.modelVisibility, [modelId]: visible }
        })),
      toggleModelVisibility: (modelId) => 
        set((state) => ({
          modelVisibility: { 
            ...state.modelVisibility, 
            [modelId]: !state.modelVisibility[modelId] 
          }
        })),
      
      // Photo Actions
      addCapturedPhoto: (photoData) => 
        set((state) => ({
          capturedPhotos: [...state.capturedPhotos, photoData]
        })),
      removeCapturedPhoto: (index) => 
        set((state) => ({
          capturedPhotos: state.capturedPhotos.filter((_, i) => i !== index)
        })),
      clearCapturedPhotos: () => set({ capturedPhotos: [] }),
      
      // Utility Actions
      reset: () => set({
        isLoading: false,
        error: null,
        isCameraActive: false,
        cameraError: null,
        isTracking: false,
        currentLandmarks: null,
        trackingError: null,
        performanceStats: initialPerformanceStats,
        selectedModel: null,
        loadedModels: new Set(),
        isARActive: false,
        modelVisibility: {},
        capturedPhotos: []
      })
    }),
    {
      name: 'virtual-try-on-store',
      partialize: (state) => ({
        // Only persist certain parts of the state
        selectedCategory: state.selectedCategory,
        capturedPhotos: state.capturedPhotos
      })
    }
  )
);

// Selectors for computed values
export const useAppSelectors = () => {
  const store = useAppStore();
  
  return {
    // Get models for current category
    getCurrentCategoryModels: () => {
      return store.modelsByCategory[store.selectedCategory] || [];
    },
    
    // Get visible models
    getVisibleModels: () => {
      return Array.from(store.loadedModels).filter(
        modelId => store.modelVisibility[modelId] !== false
      );
    },
    
    // Check if any model is loaded
    hasLoadedModels: () => {
      return store.loadedModels.size > 0;
    },
    
    // Get tracking status
    getTrackingStatus: () => {
      return {
        isActive: store.isTracking,
        hasLandmarks: !!store.currentLandmarks,
        error: store.trackingError
      };
    },
    
    // Get performance summary
    getPerformanceSummary: () => {
      const stats = store.performanceStats;
      return {
        fps: Math.round(stats.fps),
        avgProcessingTime: Math.round(
          (stats.clientAvgTime + stats.djangoAvgTime) / 2
        ),
        totalDetections: stats.clientCount + stats.djangoCount
      };
    }
  };
};
