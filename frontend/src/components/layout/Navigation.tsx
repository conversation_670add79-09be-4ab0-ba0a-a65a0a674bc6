import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';

const Nav = styled.nav`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const NavContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  
  &:hover {
    color: #ffd700;
  }
`;

const NavLinks = styled.div`
  display: flex;
  gap: 24px;
  align-items: center;
`;

const NavLink = styled(Link)<{ $isActive: boolean }>`
  color: ${props => props.$isActive ? '#ffd700' : 'white'};
  text-decoration: none;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffd700;
  }
`;

const StatusIndicator = styled.div<{ $isOnline: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.$isOnline ? '#4ade80' : '#ef4444'};
  margin-left: 8px;
`;

function Navigation() {
  const location = useLocation();
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <Nav>
      <NavContainer>
        <Logo to="/">
          <i className="fas fa-glasses" />
          Virtual Try-On
        </Logo>
        
        <NavLinks>
          <NavLink 
            to="/" 
            $isActive={location.pathname === '/'}
          >
            <i className="fas fa-home" />
            Home
          </NavLink>
          
          <NavLink
            to="/tryron"
            $isActive={location.pathname === '/tryron'}
          >
            <i className="fas fa-camera" />
            Try On
          </NavLink>

          <NavLink
            to="/webxr"
            $isActive={location.pathname === '/webxr'}
          >
            <i className="fas fa-vr-cardboard" />
            WebXR
          </NavLink>

          <StatusIndicator
            $isOnline={isOnline}
            title={isOnline ? 'Online' : 'Offline'}
          />
        </NavLinks>
      </NavContainer>
    </Nav>
  );
}

export default Navigation;
