import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useCamera } from '@hooks/useCamera';
import { useThreeJS } from '@hooks/useThreeJS';
import { useAppStore } from '@stores/appStore';

const ControlsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
`;

const ControlSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SectionTitle = styled.h3`
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const ControlButton = styled(motion.button)<{ 
  $variant?: 'primary' | 'secondary' | 'success' | 'danger';
  $isActive?: boolean;
}>`
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
  
  background: ${props => {
    if (props.$isActive) return 'linear-gradient(45deg, #10b981, #059669)';
    switch (props.$variant) {
      case 'primary': return 'linear-gradient(45deg, #667eea, #764ba2)';
      case 'success': return 'linear-gradient(45deg, #10b981, #059669)';
      case 'danger': return 'linear-gradient(45deg, #ef4444, #dc2626)';
      case 'secondary':
      default: return 'rgba(255, 255, 255, 0.2)';
    }
  }};
  
  color: white;
  border: 1px solid ${props => props.$isActive ? '#10b981' : 'rgba(255, 255, 255, 0.3)'};

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const PhotoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
`;

const PhotoThumbnail = styled.div`
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const PhotoActions = styled.div`
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  gap: 4px;
`;

const PhotoActionButton = styled.button`
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
  }
`;

const StatusIndicator = styled.div<{ $status: 'active' | 'inactive' | 'error' }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  
  background: ${props => {
    switch (props.$status) {
      case 'active': return 'rgba(16, 185, 129, 0.2)';
      case 'error': return 'rgba(239, 68, 68, 0.2)';
      default: return 'rgba(107, 114, 128, 0.2)';
    }
  }};
  
  color: ${props => {
    switch (props.$status) {
      case 'active': return '#10b981';
      case 'error': return '#ef4444';
      default: return '#9ca3af';
    }
  }};
  
  border: 1px solid ${props => {
    switch (props.$status) {
      case 'active': return 'rgba(16, 185, 129, 0.3)';
      case 'error': return 'rgba(239, 68, 68, 0.3)';
      default: return 'rgba(107, 114, 128, 0.3)';
    }
  }};
`;

const CameraControls: React.FC = () => {
  const { takePhoto } = useCamera();
  const { captureScreenshot } = useThreeJS();
  const {
    isCameraActive,
    isTracking,
    isARActive,
    capturedPhotos,
    addCapturedPhoto,
    removeCapturedPhoto,
    clearCapturedPhotos
  } = useAppStore();

  const [isCapturing, setIsCapturing] = useState(false);

  const handleTakePhoto = async () => {
    if (!isCameraActive || isCapturing) return;

    setIsCapturing(true);
    try {
      // Capture both camera and 3D overlay
      const cameraPhoto = takePhoto();
      const overlayPhoto = captureScreenshot();

      if (cameraPhoto) {
        // For now, just save the camera photo
        // In a more advanced implementation, you could composite both images
        addCapturedPhoto(cameraPhoto);
        
        // Create download link
        const link = document.createElement('a');
        link.href = cameraPhoto;
        link.download = `virtual-tryron-${Date.now()}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Photo capture failed:', error);
    } finally {
      setIsCapturing(false);
    }
  };

  const handleDownloadPhoto = (photoData: string, index: number) => {
    const link = document.createElement('a');
    link.href = photoData;
    link.download = `virtual-tryron-${index + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getStatusInfo = () => {
    if (!isCameraActive) {
      return { status: 'inactive' as const, text: 'Camera Inactive', icon: 'fas fa-video-slash' };
    }
    if (!isTracking) {
      return { status: 'inactive' as const, text: 'Face Detection Off', icon: 'fas fa-user-slash' };
    }
    if (isARActive) {
      return { status: 'active' as const, text: 'AR Active', icon: 'fas fa-check-circle' };
    }
    return { status: 'inactive' as const, text: 'Initializing...', icon: 'fas fa-spinner fa-spin' };
  };

  const statusInfo = getStatusInfo();

  return (
    <ControlsContainer>
      {/* Status Section */}
      <ControlSection>
        <SectionTitle>
          <i className="fas fa-info-circle" />
          Status
        </SectionTitle>
        <StatusIndicator $status={statusInfo.status}>
          <i className={statusInfo.icon} />
          {statusInfo.text}
        </StatusIndicator>
      </ControlSection>

      {/* Camera Controls */}
      <ControlSection>
        <SectionTitle>
          <i className="fas fa-camera" />
          Camera
        </SectionTitle>
        <ButtonGroup>
          <ControlButton
            $variant="primary"
            disabled={!isARActive || isCapturing}
            onClick={handleTakePhoto}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isCapturing ? (
              <>
                <div className="spinner" style={{ width: '16px', height: '16px' }} />
                Capturing...
              </>
            ) : (
              <>
                <i className="fas fa-camera" />
                Take Photo
              </>
            )}
          </ControlButton>
        </ButtonGroup>
      </ControlSection>

      {/* Photo Gallery */}
      {capturedPhotos.length > 0 && (
        <ControlSection>
          <SectionTitle>
            <i className="fas fa-images" />
            Photos ({capturedPhotos.length})
            <ControlButton
              $variant="secondary"
              onClick={clearCapturedPhotos}
              style={{ marginLeft: 'auto', minWidth: 'auto', padding: '4px 8px' }}
            >
              <i className="fas fa-trash" />
            </ControlButton>
          </SectionTitle>
          
          <PhotoGrid>
            {capturedPhotos.map((photo, index) => (
              <PhotoThumbnail key={index}>
                <img src={photo} alt={`Captured photo ${index + 1}`} />
                <PhotoActions>
                  <PhotoActionButton
                    onClick={() => handleDownloadPhoto(photo, index)}
                    title="Download"
                  >
                    <i className="fas fa-download" />
                  </PhotoActionButton>
                  <PhotoActionButton
                    onClick={() => removeCapturedPhoto(index)}
                    title="Delete"
                  >
                    <i className="fas fa-times" />
                  </PhotoActionButton>
                </PhotoActions>
              </PhotoThumbnail>
            ))}
          </PhotoGrid>
        </ControlSection>
      )}

      {/* Performance Info */}
      {isTracking && (
        <ControlSection>
          <SectionTitle>
            <i className="fas fa-tachometer-alt" />
            Performance
          </SectionTitle>
          <StatusIndicator $status="active">
            <i className="fas fa-chart-line" />
            Real-time tracking active
          </StatusIndicator>
        </ControlSection>
      )}
    </ControlsContainer>
  );
};

export default CameraControls;
