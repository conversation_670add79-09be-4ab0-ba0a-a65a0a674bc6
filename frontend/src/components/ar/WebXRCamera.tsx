import React, { useEffect, useCallback, useState } from 'react';
import styled from 'styled-components';
import { useCamera } from '@hooks/useCamera';
import { useFaceTracking } from '@hooks/useFaceTracking';
import { useThreeJS } from '@hooks/useThreeJS';
import { useWebXR } from '@hooks/useWebXR';
import { useAppStore } from '@stores/appStore';

const WebXRContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  border-radius: 16px;
  overflow: hidden;
  background: #000;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scaleX(-1); /* Mirror the video */
  display: ${props => props.hidden ? 'none' : 'block'};
`;

const ThreeJSOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
`;

const WebXRControls = styled.div`
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 20;
`;

const WebXRButton = styled.button<{ $isActive?: boolean; $isSupported?: boolean }>`
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: ${props => 
    props.$isActive ? '#4CAF50' : 
    props.$isSupported ? '#2196F3' : '#666'
  };
  color: white;
  font-weight: 600;
  cursor: ${props => props.$isSupported ? 'pointer' : 'not-allowed'};
  opacity: ${props => props.$isSupported ? 1 : 0.5};
  transition: all 0.3s ease;

  &:hover {
    background: ${props => 
      props.$isActive ? '#45a049' : 
      props.$isSupported ? '#1976D2' : '#666'
    };
    transform: ${props => props.$isSupported ? 'translateY(-2px)' : 'none'};
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const StatusOverlay = styled.div`
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 15;
`;

const StatusCard = styled.div`
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
`;

const PerformanceStats = styled.div`
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 12px;
  margin-top: 12px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
`;

interface WebXRCameraProps {
  className?: string;
  enableAR?: boolean;
  enableVR?: boolean;
  enableFallback?: boolean;
}

const WebXRCamera: React.FC<WebXRCameraProps> = ({ 
  className, 
  enableAR = true, 
  enableVR = true,
  enableFallback = true 
}) => {
  const [currentMode, setCurrentMode] = useState<'2D' | 'AR' | 'VR'>('2D');

  const {
    videoRef,
    isActive: isCameraActive,
    error: cameraError,
    startCamera,
    stopCamera
  } = useCamera({
    facingMode: 'user',
    width: 640,
    height: 480
  });

  const {
    landmarks,
    isTracking,
    error: trackingError,
    performance,
    startTracking,
    stopTracking
  } = useFaceTracking({
    enableClientSide: false,
    enableServerSide: false,
    detectionInterval: 2000,
    confidenceThreshold: 0.7
  });

  const {
    containerRef,
    isInitialized: isThreeJSInitialized,
    loadedModels,
    updateModelTransform,
    isWebXRSupported,
    isWebXRActive,
    controllers,
    hands,
    enableWebXR,
    getWebXRButton
  } = useThreeJS({
    enableShadows: true,
    enableAntialiasing: true,
    backgroundColor: 0x000000,
    cameraFov: 75,
    enableWebXR: true,
    webXRMode: 'both'
  });

  const {
    isSupported: isWebXRAPISupported,
    isSessionActive,
    sessionMode,
    capabilities,
    startSession,
    endSession,
    error: webXRError
  } = useWebXR({
    requiredFeatures: [],
    optionalFeatures: ['hand-tracking', 'face-tracking', 'hit-test', 'dom-overlay'],
    enableHandTracking: true,
    enableFaceTracking: true
  });

  const {
    setCameraActive,
    setCameraError,
    setTracking,
    setCurrentLandmarks,
    setTrackingError,
    updatePerformanceStats,
    setARActive
  } = useAppStore();

  // Update store when camera state changes
  useEffect(() => {
    setCameraActive(isCameraActive);
    setCameraError(cameraError);
  }, [isCameraActive, cameraError, setCameraActive, setCameraError]);

  // Update store when tracking state changes
  useEffect(() => {
    setTracking(isTracking);
    setCurrentLandmarks(landmarks);
    setTrackingError(trackingError);
    updatePerformanceStats(performance);
  }, [isTracking, landmarks, trackingError, performance, setTracking, setCurrentLandmarks, setTrackingError, updatePerformanceStats]);

  // Update AR active state
  useEffect(() => {
    const isARActive = (isCameraActive && isTracking && isThreeJSInitialized) || isWebXRActive;
    setARActive(isARActive);
  }, [isCameraActive, isTracking, isThreeJSInitialized, isWebXRActive, setARActive]);

  // Update 3D models based on face landmarks
  useEffect(() => {
    if (landmarks && loadedModels.size > 0) {
      loadedModels.forEach((model, modelId) => {
        updateModelTransform(modelId, landmarks);
      });
    }
  }, [landmarks, loadedModels, updateModelTransform]);

  // Start camera and tracking when component mounts (fallback mode)
  useEffect(() => {
    if (enableFallback && !isWebXRActive) {
      let isMounted = true;
      let timeoutId: NodeJS.Timeout;

      const initializeCamera = async () => {
        try {
          if (isMounted && !isCameraActive) {
            timeoutId = setTimeout(async () => {
              if (isMounted && !isCameraActive) {
                await startCamera();
              }
            }, 100);
          }
        } catch (error) {
          if (isMounted) {
            console.error('Failed to start camera:', error);
          }
        }
      };

      initializeCamera();

      return () => {
        isMounted = false;
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    }
  }, [enableFallback, isWebXRActive, isCameraActive, startCamera]);

  // Start face tracking when camera is ready (fallback mode)
  useEffect(() => {
    if (enableFallback && !isWebXRActive && isCameraActive && videoRef.current && !isTracking) {
      startTracking(videoRef.current);
    } else if ((!isCameraActive || isWebXRActive) && isTracking) {
      stopTracking();
    }
  }, [enableFallback, isWebXRActive, isCameraActive, isTracking, startTracking, stopTracking]);

  // Handle WebXR session changes
  useEffect(() => {
    if (isSessionActive) {
      setCurrentMode(sessionMode === 'immersive-ar' ? 'AR' : 'VR');
      // Stop camera when in WebXR mode
      if (isCameraActive) {
        stopCamera();
      }
    } else {
      setCurrentMode('2D');
      // Restart camera when exiting WebXR mode
      if (enableFallback && !isCameraActive) {
        startCamera();
      }
    }
  }, [isSessionActive, sessionMode, isCameraActive, stopCamera, startCamera, enableFallback]);

  const handleStartAR = useCallback(async () => {
    try {
      await startSession('immersive-ar');
    } catch (error) {
      console.error('Failed to start AR session:', error);
    }
  }, [startSession]);

  const handleStartVR = useCallback(async () => {
    try {
      await startSession('immersive-vr');
    } catch (error) {
      console.error('Failed to start VR session:', error);
    }
  }, [startSession]);

  const handleEndSession = useCallback(async () => {
    try {
      await endSession();
    } catch (error) {
      console.error('Failed to end WebXR session:', error);
    }
  }, [endSession]);

  const getStatusIcon = () => {
    if (cameraError || trackingError || webXRError) return 'fas fa-exclamation-triangle';
    if (isWebXRActive) return 'fas fa-vr-cardboard';
    if (!isCameraActive && enableFallback) return 'fas fa-video-slash';
    if (!isTracking && enableFallback) return 'fas fa-search';
    if (!landmarks && enableFallback) return 'fas fa-user-slash';
    return 'fas fa-check-circle';
  };

  const getStatusColor = () => {
    if (cameraError || trackingError || webXRError) return '#f44336';
    if (isWebXRActive) return '#4CAF50';
    if ((!isCameraActive || !isTracking || !landmarks) && enableFallback) return '#ff9800';
    return '#4CAF50';
  };

  const getStatusText = () => {
    if (webXRError) return `WebXR Error: ${webXRError}`;
    if (cameraError) return `Camera Error: ${cameraError}`;
    if (trackingError) return `Tracking Error: ${trackingError}`;
    if (isWebXRActive) return `WebXR ${currentMode} Active`;
    if (!isCameraActive && enableFallback) return 'Camera not active';
    if (!isTracking && enableFallback) return 'Face tracking inactive';
    if (!landmarks && enableFallback) return 'No face detected';
    return enableFallback ? 'AR Ready' : 'WebXR Ready';
  };

  return (
    <WebXRContainer className={className}>
      {/* Video Stream (hidden when in WebXR mode) */}
      {enableFallback && (
        <VideoElement
          ref={videoRef}
          autoPlay
          playsInline
          muted
          hidden={isWebXRActive}
        />
      )}

      {/* Three.js 3D Overlay */}
      <ThreeJSOverlay ref={containerRef} />

      {/* Status Overlay */}
      <StatusOverlay>
        <StatusCard>
          <i 
            className={getStatusIcon()} 
            style={{ color: getStatusColor() }}
          />
          {getStatusText()}
        </StatusCard>

        {/* Performance Stats */}
        {(isTracking || isWebXRActive) && (
          <PerformanceStats>
            <div>Mode: {currentMode}</div>
            {enableFallback && <div>FPS: {Math.round(performance.fps)}</div>}
            {enableFallback && <div>Client: {Math.round(performance.clientAvgTime)}ms</div>}
            {isWebXRActive && <div>Controllers: {controllers.length}</div>}
            {isWebXRActive && <div>Hands: {hands.length}</div>}
          </PerformanceStats>
        )}
      </StatusOverlay>

      {/* WebXR Controls */}
      <WebXRControls>
        {enableAR && (
          <WebXRButton
            $isActive={isSessionActive && sessionMode === 'immersive-ar'}
            $isSupported={capabilities.supportsAR}
            onClick={isSessionActive ? handleEndSession : handleStartAR}
            disabled={!capabilities.supportsAR}
          >
            {isSessionActive && sessionMode === 'immersive-ar' ? 'Exit AR' : 'Enter AR'}
          </WebXRButton>
        )}

        {enableVR && (
          <WebXRButton
            $isActive={isSessionActive && sessionMode === 'immersive-vr'}
            $isSupported={capabilities.supportsVR}
            onClick={isSessionActive ? handleEndSession : handleStartVR}
            disabled={!capabilities.supportsVR}
          >
            {isSessionActive && sessionMode === 'immersive-vr' ? 'Exit VR' : 'Enter VR'}
          </WebXRButton>
        )}
      </WebXRControls>
    </WebXRContainer>
  );
};

export default WebXRCamera;
