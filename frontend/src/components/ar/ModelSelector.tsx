import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useQuery } from 'react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '@stores/appStore';
import { useThreeJS } from '@hooks/useThreeJS';
import { apiService } from '@services/api';
import { AccessoryModel, AccessoryCategory } from '@types/index';
import { simpleModelGenerator } from '@utils/simpleModels';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  overflow: hidden;
`;

const CategoryTabs = styled.div`
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
`;

const CategoryTab = styled.button<{ $isActive: boolean }>`
  flex: 1;
  padding: 12px 16px;
  background: ${props => props.$isActive ? 'rgba(255, 255, 255, 0.2)' : 'transparent'};
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const ModelsGrid = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  max-height: 400px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const ModelCard = styled(motion.div)<{ $isSelected: boolean; $isLoaded: boolean }>`
  background: ${props => 
    props.$isSelected ? 'rgba(102, 126, 234, 0.3)' : 
    props.$isLoaded ? 'rgba(16, 185, 129, 0.2)' : 
    'rgba(255, 255, 255, 0.1)'
  };
  border: 1px solid ${props => 
    props.$isSelected ? 'rgba(102, 126, 234, 0.5)' : 
    props.$isLoaded ? 'rgba(16, 185, 129, 0.3)' : 
    'rgba(255, 255, 255, 0.2)'
  };
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }
`;

const ModelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const ModelName = styled.h4`
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
`;

const ModelBadges = styled.div`
  display: flex;
  gap: 4px;
  flex-shrink: 0;
`;

const Badge = styled.span<{ $type: 'featured' | 'loaded' | 'selected' }>`
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 4px;
  background: ${props => {
    switch (props.$type) {
      case 'featured': return '#ffd700';
      case 'loaded': return '#10b981';
      case 'selected': return '#667eea';
      default: return '#6b7280';
    }
  }};
  color: ${props => props.$type === 'featured' ? '#000' : '#fff'};
  font-weight: 600;
`;

const ModelInfo = styled.div`
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
`;

const ModelActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 8px;
`;

const ActionButton = styled.button<{ $variant: 'primary' | 'secondary' | 'danger' }>`
  flex: 1;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  background: ${props => {
    switch (props.$variant) {
      case 'primary': return 'linear-gradient(45deg, #667eea, #764ba2)';
      case 'secondary': return 'rgba(255, 255, 255, 0.2)';
      case 'danger': return 'linear-gradient(45deg, #ff6b6b, #ee5a24)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  
  color: white;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const LoadingState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: white;
  opacity: 0.7;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: white;
  opacity: 0.7;
  text-align: center;
`;

const ModelSelector: React.FC = () => {
  const {
    selectedCategory,
    selectedModel,
    loadedModels,
    setSelectedCategory,
    setSelectedModel,
    addLoadedModel,
    removeLoadedModel,
    setModelVisibility
  } = useAppStore();

  const { loadModel, removeModel } = useThreeJS();
  const [loadingModels, setLoadingModels] = useState<Set<string>>(new Set());

  // Fetch models data
  const { data: modelsData, isLoading: isLoadingModels } = useQuery(
    'models',
    apiService.getModelsList,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Fetch categories
  const { data: categories } = useQuery(
    'categories',
    apiService.getCategories,
    {
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Combine API models with simple models as fallbacks
  const apiModels = modelsData?.[selectedCategory] || [];
  const simpleModels = simpleModelGenerator.getAvailableModels()[selectedCategory] || [];
  const currentModels = [...apiModels, ...simpleModels];

  const handleModelSelect = async (model: AccessoryModel) => {
    if (loadingModels.has(model.id)) return;

    setSelectedModel(model);

    // If model is already loaded, just toggle visibility
    if (loadedModels.has(model.id)) {
      setModelVisibility(model.id, true);
      return;
    }

    // Load the model
    setLoadingModels(prev => new Set([...prev, model.id]));
    
    try {
      await loadModel(model);
      addLoadedModel(model.id);
      setModelVisibility(model.id, true);
      
      // Update model usage count
      await apiService.updateModelUsage(model.id);
    } catch (error) {
      console.error('Failed to load model:', error);
    } finally {
      setLoadingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(model.id);
        return newSet;
      });
    }
  };

  const handleModelRemove = (model: AccessoryModel) => {
    removeModel(model.id);
    removeLoadedModel(model.id);
    
    if (selectedModel?.id === model.id) {
      setSelectedModel(null);
    }
  };

  const getCategoryIcon = (categorySlug: string) => {
    switch (categorySlug) {
      case 'glasses': return 'fas fa-glasses';
      case 'hats': return 'fas fa-hat-cowboy';
      case 'earrings': return 'fas fa-gem';
      case 'necklaces': return 'fas fa-necklace';
      default: return 'fas fa-cube';
    }
  };

  if (isLoadingModels) {
    return (
      <SelectorContainer>
        <LoadingState>
          <div className="spinner" style={{ marginRight: '12px' }} />
          Loading models...
        </LoadingState>
      </SelectorContainer>
    );
  }

  return (
    <SelectorContainer>
      {/* Category Tabs */}
      <CategoryTabs>
        {categories?.map((category) => (
          <CategoryTab
            key={category.slug}
            $isActive={selectedCategory === category.slug}
            onClick={() => setSelectedCategory(category.slug)}
          >
            <i className={getCategoryIcon(category.slug)} />
            {category.name}
          </CategoryTab>
        ))}
      </CategoryTabs>

      {/* Models Grid */}
      <ModelsGrid>
        <AnimatePresence>
          {currentModels.length === 0 ? (
            <EmptyState>
              <i className="fas fa-box-open" style={{ fontSize: '2rem', marginBottom: '12px' }} />
              <div>No models available in this category</div>
            </EmptyState>
          ) : (
            currentModels.map((model) => {
              const isSelected = selectedModel?.id === model.id;
              const isLoaded = loadedModels.has(model.id);
              const isLoading = loadingModels.has(model.id);

              return (
                <ModelCard
                  key={model.id}
                  $isSelected={isSelected}
                  $isLoaded={isLoaded}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  onClick={() => handleModelSelect(model)}
                >
                  <ModelHeader>
                    <ModelName>{model.name}</ModelName>
                    <ModelBadges>
                      {model.is_featured && <Badge $type="featured">★</Badge>}
                      {isLoaded && <Badge $type="loaded">✓</Badge>}
                      {isSelected && <Badge $type="selected">●</Badge>}
                    </ModelBadges>
                  </ModelHeader>

                  <ModelInfo>
                    {model.description && <div>{model.description}</div>}
                    <div>Size: {model.file_size_mb.toFixed(1)}MB</div>
                    {model.average_rating > 0 && (
                      <div>Rating: {model.average_rating.toFixed(1)}/5</div>
                    )}
                  </ModelInfo>

                  <ModelActions>
                    {isLoading ? (
                      <ActionButton $variant="secondary" disabled>
                        <div className="spinner" style={{ width: '12px', height: '12px' }} />
                        Loading...
                      </ActionButton>
                    ) : isLoaded ? (
                      <ActionButton
                        $variant="danger"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleModelRemove(model);
                        }}
                      >
                        Remove
                      </ActionButton>
                    ) : (
                      <ActionButton $variant="primary">
                        Try On
                      </ActionButton>
                    )}
                  </ModelActions>
                </ModelCard>
              );
            })
          )}
        </AnimatePresence>
      </ModelsGrid>
    </SelectorContainer>
  );
};

export default ModelSelector;
