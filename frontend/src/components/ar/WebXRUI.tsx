import React, { useEffect, useRef, useCallback } from 'react';
import * as THREE from 'three';
import { useThreeJS } from '@hooks/useThreeJS';
import { useWebXR } from '@hooks/useWebXR';
import { useAppStore } from '@stores/appStore';

interface WebXRUIProps {
  visible?: boolean;
  position?: [number, number, number];
  scale?: number;
}

interface UIPanel {
  mesh: THREE.Mesh;
  canvas: HTMLCanvasElement;
  context: CanvasRenderingContext2D;
  texture: THREE.CanvasTexture;
}

const WebXRUI: React.FC<WebXRUIProps> = ({ 
  visible = true, 
  position = [0, 0, -1], 
  scale = 1 
}) => {
  const uiPanelRef = useRef<UIPanel | null>(null);
  const uiGroupRef = useRef<THREE.Group | null>(null);

  const { scene, isWebXRActive, controllers, hands } = useThreeJS();
  const { isSessionActive, sessionMode } = useWebXR();
  const { 
    selectedCategory, 
    selectedModel, 
    loadedModels,
    setSelectedCategory,
    setSelectedModel 
  } = useAppStore();

  // Create UI panel
  const createUIPanel = useCallback(() => {
    if (!scene) return null;

    // Create canvas for UI rendering
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const context = canvas.getContext('2d');
    
    if (!context) return null;

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    // Create material and geometry
    const material = new THREE.MeshBasicMaterial({ 
      map: texture, 
      transparent: true,
      alphaTest: 0.1
    });
    const geometry = new THREE.PlaneGeometry(1, 1);
    const mesh = new THREE.Mesh(geometry, material);

    // Create UI group
    const uiGroup = new THREE.Group();
    uiGroup.add(mesh);
    uiGroup.position.set(...position);
    uiGroup.scale.setScalar(scale);
    
    // Make UI face the user
    uiGroup.lookAt(0, 0, 0);

    scene.add(uiGroup);
    uiGroupRef.current = uiGroup;

    return {
      mesh,
      canvas,
      context,
      texture
    };
  }, [scene, position, scale]);

  // Render UI content to canvas
  const renderUIContent = useCallback((panel: UIPanel) => {
    const { canvas, context, texture } = panel;
    
    // Clear canvas
    context.clearRect(0, 0, canvas.width, canvas.height);

    // Set up styling
    context.fillStyle = 'rgba(0, 0, 0, 0.8)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    // Draw border
    context.strokeStyle = '#4CAF50';
    context.lineWidth = 4;
    context.strokeRect(0, 0, canvas.width, canvas.height);

    // Draw title
    context.fillStyle = '#ffffff';
    context.font = 'bold 32px Arial';
    context.textAlign = 'center';
    context.fillText('Virtual Try-On', canvas.width / 2, 50);

    // Draw session info
    context.font = '24px Arial';
    context.fillText(`Mode: ${sessionMode || 'Unknown'}`, canvas.width / 2, 100);

    // Draw category selection
    context.font = '20px Arial';
    context.fillStyle = '#4CAF50';
    context.fillText('Categories:', canvas.width / 2, 150);

    const categories = ['glasses', 'hats', 'accessories'];
    categories.forEach((category, index) => {
      const y = 180 + (index * 40);
      const isSelected = selectedCategory === category;
      
      context.fillStyle = isSelected ? '#4CAF50' : '#ffffff';
      context.fillText(category.charAt(0).toUpperCase() + category.slice(1), canvas.width / 2, y);
      
      if (isSelected) {
        context.strokeStyle = '#4CAF50';
        context.lineWidth = 2;
        context.strokeRect(canvas.width / 2 - 80, y - 25, 160, 35);
      }
    });

    // Draw model info
    if (selectedModel) {
      context.fillStyle = '#ffffff';
      context.font = '18px Arial';
      context.fillText('Selected Model:', canvas.width / 2, 320);
      context.fillText(selectedModel.name, canvas.width / 2, 350);
    }

    // Draw loaded models count
    context.fillStyle = '#4CAF50';
    context.font = '16px Arial';
    context.fillText(`Loaded Models: ${loadedModels.size}`, canvas.width / 2, 400);

    // Draw controller instructions
    context.fillStyle = '#ffffff';
    context.font = '14px Arial';
    context.fillText('Point and select with controllers', canvas.width / 2, 450);
    context.fillText('Use hand gestures to interact', canvas.width / 2, 470);

    // Update texture
    texture.needsUpdate = true;
  }, [sessionMode, selectedCategory, selectedModel, loadedModels]);

  // Handle controller interactions
  const handleControllerInteraction = useCallback((controller: THREE.Group) => {
    if (!uiPanelRef.current || !uiGroupRef.current) return;

    // Create raycaster for controller
    const raycaster = new THREE.Raycaster();
    const tempMatrix = new THREE.Matrix4();
    
    // Get controller position and direction
    tempMatrix.identity().extractRotation(controller.matrixWorld);
    raycaster.ray.origin.setFromMatrixPosition(controller.matrixWorld);
    raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix);

    // Check intersection with UI panel
    const intersects = raycaster.intersectObject(uiPanelRef.current.mesh);
    
    if (intersects.length > 0) {
      const intersection = intersects[0];
      const uv = intersection.uv;
      
      if (uv) {
        // Convert UV coordinates to canvas coordinates
        const x = uv.x * uiPanelRef.current.canvas.width;
        const y = (1 - uv.y) * uiPanelRef.current.canvas.height;
        
        // Handle UI interactions based on position
        handleUIClick(x, y);
      }
    }
  }, []);

  // Handle UI clicks
  const handleUIClick = useCallback((x: number, y: number) => {
    const canvas = uiPanelRef.current?.canvas;
    if (!canvas) return;

    // Category selection area (y: 160-260)
    if (y >= 160 && y <= 260) {
      const categories = ['glasses', 'hats', 'accessories'];
      const categoryIndex = Math.floor((y - 160) / 40);
      
      if (categoryIndex >= 0 && categoryIndex < categories.length) {
        setSelectedCategory(categories[categoryIndex]);
      }
    }
  }, [setSelectedCategory]);

  // Set up controller event listeners
  useEffect(() => {
    if (!isWebXRActive || controllers.length === 0) return;

    const handleSelectStart = (event: any) => {
      const controller = event.target;
      handleControllerInteraction(controller);
    };

    controllers.forEach(controller => {
      controller.addEventListener('selectstart', handleSelectStart);
    });

    return () => {
      controllers.forEach(controller => {
        controller.removeEventListener('selectstart', handleSelectStart);
      });
    };
  }, [isWebXRActive, controllers, handleControllerInteraction]);

  // Initialize UI panel
  useEffect(() => {
    if (isWebXRActive && visible && !uiPanelRef.current) {
      const panel = createUIPanel();
      if (panel) {
        uiPanelRef.current = panel;
        renderUIContent(panel);
      }
    } else if (!isWebXRActive && uiPanelRef.current) {
      // Clean up UI panel when exiting WebXR
      if (scene && uiGroupRef.current) {
        scene.remove(uiGroupRef.current);
      }
      uiPanelRef.current = null;
      uiGroupRef.current = null;
    }
  }, [isWebXRActive, visible, createUIPanel, renderUIContent, scene]);

  // Update UI content when state changes
  useEffect(() => {
    if (uiPanelRef.current && isWebXRActive) {
      renderUIContent(uiPanelRef.current);
    }
  }, [selectedCategory, selectedModel, loadedModels, sessionMode, renderUIContent, isWebXRActive]);

  // Position UI panel relative to user
  useEffect(() => {
    if (uiGroupRef.current && isWebXRActive) {
      // Update position to stay in front of user
      uiGroupRef.current.position.set(...position);
      uiGroupRef.current.lookAt(0, 0, 0);
    }
  }, [position, isWebXRActive]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scene && uiGroupRef.current) {
        scene.remove(uiGroupRef.current);
      }
    };
  }, [scene]);

  // This component doesn't render anything to the DOM
  // All rendering is done in the Three.js scene
  return null;
};

export default WebXRUI;
