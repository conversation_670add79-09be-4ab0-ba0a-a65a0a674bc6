import React, { useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { useCamera } from '@hooks/useCamera';
import { useFaceTracking } from '@hooks/useFaceTracking';
import { useThreeJS } from '@hooks/useThreeJS';
import { useAppStore } from '@stores/appStore';

const CameraContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  border-radius: 16px;
  overflow: hidden;
  background: #000;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scaleX(-1); /* Mirror the video */
`;

const ThreeJSOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  
  canvas {
    width: 100% !important;
    height: 100% !important;
  }
`;

const StatusOverlay = styled.div`
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  z-index: 3;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;

const StatusCard = styled.div`
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PerformanceStats = styled.div`
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-size: 0.8rem;
  font-family: monospace;
`;

const ErrorMessage = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  text-align: center;
  z-index: 4;
  max-width: 300px;
`;

const LoadingSpinner = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
`;

interface ARCameraProps {
  className?: string;
}

const ARCamera: React.FC<ARCameraProps> = ({ className }) => {
  const {
    videoRef,
    isActive: isCameraActive,
    error: cameraError,
    startCamera,
    stopCamera
  } = useCamera({
    facingMode: 'user',
    width: 640,
    height: 480
  });

  const {
    landmarks,
    isTracking,
    error: trackingError,
    performance,
    startTracking,
    stopTracking
  } = useFaceTracking({
    enableClientSide: false, // Disabled due to import issues
    enableServerSide: false, // Disabled for now - working but can cause performance issues
    detectionInterval: 2000, // Much slower interval for server-side (2 seconds)
    confidenceThreshold: 0.7
  });

  const {
    containerRef,
    isInitialized: isThreeJSInitialized,
    loadedModels,
    updateModelTransform
  } = useThreeJS({
    enableShadows: true,
    enableAntialiasing: true,
    backgroundColor: 0x000000,
    cameraFov: 75
  });

  const {
    setCameraActive,
    setCameraError,
    setTracking,
    setCurrentLandmarks,
    setTrackingError,
    updatePerformanceStats,
    setARActive
  } = useAppStore();

  // Update store when camera state changes
  useEffect(() => {
    setCameraActive(isCameraActive);
    setCameraError(cameraError);
  }, [isCameraActive, cameraError, setCameraActive, setCameraError]);

  // Update store when tracking state changes
  useEffect(() => {
    setTracking(isTracking);
    setCurrentLandmarks(landmarks);
    setTrackingError(trackingError);
    updatePerformanceStats(performance);
  }, [isTracking, landmarks, trackingError, performance, setTracking, setCurrentLandmarks, setTrackingError, updatePerformanceStats]);

  // Update AR active state
  useEffect(() => {
    setARActive(isCameraActive && isTracking && isThreeJSInitialized);
  }, [isCameraActive, isTracking, isThreeJSInitialized, setARActive]);

  // Update 3D models based on face landmarks
  useEffect(() => {
    if (landmarks && loadedModels.size > 0) {
      loadedModels.forEach((model, modelId) => {
        updateModelTransform(modelId, landmarks);
      });
    }
  }, [landmarks, loadedModels, updateModelTransform]);

  // Start camera and tracking when component mounts
  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const initializeAR = async () => {
      try {
        if (isMounted && !isCameraActive) {
          // Small delay to avoid rapid re-initialization in React StrictMode
          timeoutId = setTimeout(async () => {
            if (isMounted && !isCameraActive) {
              await startCamera();
            }
          }, 100);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Failed to start camera:', error);
        }
      }
    };

    initializeAR();

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      stopCamera();
      stopTracking();
    };
  }, []); // Empty dependency array to run only once

  // Start face tracking when camera is ready
  useEffect(() => {
    if (isCameraActive && videoRef.current && !isTracking) {
      startTracking(videoRef.current);
    } else if (!isCameraActive && isTracking) {
      stopTracking();
    }
  }, [isCameraActive, isTracking, startTracking, stopTracking]);

  const getStatusIcon = () => {
    if (cameraError || trackingError) return 'fas fa-exclamation-triangle';
    if (!isCameraActive) return 'fas fa-video-slash';
    if (!isTracking) return 'fas fa-search';
    if (!landmarks) return 'fas fa-user-slash';
    return 'fas fa-check-circle';
  };

  const getStatusText = () => {
    if (cameraError) return `Camera Error: ${cameraError}`;
    if (trackingError) return `Tracking Error: ${trackingError}`;
    if (!isCameraActive) return 'Camera Inactive';
    if (!isTracking) return 'Starting Face Detection...';
    if (!landmarks) return 'No Face Detected';
    return 'Face Detected';
  };

  const getStatusColor = () => {
    if (cameraError || trackingError) return '#ef4444';
    if (!isCameraActive || !landmarks) return '#f59e0b';
    return '#10b981';
  };

  return (
    <CameraContainer className={className}>
      {/* Video Stream */}
      <VideoElement
        ref={videoRef}
        autoPlay
        playsInline
        muted
      />

      {/* Three.js 3D Overlay */}
      <ThreeJSOverlay ref={containerRef} />

      {/* Status Overlay */}
      <StatusOverlay>
        <StatusCard>
          <i 
            className={getStatusIcon()} 
            style={{ color: getStatusColor() }}
          />
          {getStatusText()}
        </StatusCard>

        {/* Performance Stats */}
        {isTracking && (
          <PerformanceStats>
            <div>FPS: {Math.round(performance.fps)}</div>
            <div>Client: {Math.round(performance.clientAvgTime)}ms</div>
            <div>Detections: {performance.clientCount}</div>
          </PerformanceStats>
        )}
      </StatusOverlay>

      {/* Error Message */}
      {(cameraError || trackingError) && (
        <ErrorMessage>
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '8px' }} />
          {cameraError || trackingError}
        </ErrorMessage>
      )}

      {/* Loading Spinner */}
      {!isCameraActive && !cameraError && (
        <LoadingSpinner>
          <div className="spinner" />
        </LoadingSpinner>
      )}
    </CameraContainer>
  );
};

export default ARCamera;
