import React, { useEffect, useRef, useCallback, useState } from 'react';
import * as THREE from 'three';
import { useThreeJS } from '@hooks/useThreeJS';
import { useWebXR } from '@hooks/useWebXR';
import { useAppStore } from '@stores/appStore';
import { AccessoryModel } from '@types/index';

interface WebXRModelSelectorProps {
  visible?: boolean;
  position?: [number, number, number];
  models?: AccessoryModel[];
}

interface ModelPreview {
  mesh: THREE.Mesh;
  model: AccessoryModel;
  originalPosition: THREE.Vector3;
  isHovered: boolean;
  isSelected: boolean;
}

const WebXRModelSelector: React.FC<WebXRModelSelectorProps> = ({
  visible = true,
  position = [1, 0, -1],
  models = []
}) => {
  const selectorGroupRef = useRef<THREE.Group | null>(null);
  const modelPreviewsRef = useRef<ModelPreview[]>([]);
  const [hoveredModel, setHoveredModel] = useState<string | null>(null);

  const { scene, isWebXRActive, controllers, loadModel } = useThreeJS();
  const { isSessionActive } = useWebXR();
  const { 
    selectedModel, 
    setSelectedModel,
    addLoadedModel,
    setModelVisibility 
  } = useAppStore();

  // Create model preview grid
  const createModelPreviews = useCallback(() => {
    if (!scene || !models.length) return;

    // Create selector group
    const selectorGroup = new THREE.Group();
    selectorGroup.position.set(...position);
    scene.add(selectorGroup);
    selectorGroupRef.current = selectorGroup;

    // Clear existing previews
    modelPreviewsRef.current = [];

    // Create grid of model previews
    const gridSize = Math.ceil(Math.sqrt(models.length));
    const spacing = 0.3;
    const startX = -(gridSize - 1) * spacing / 2;
    const startY = (gridSize - 1) * spacing / 2;

    models.forEach((model, index) => {
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;
      
      const x = startX + col * spacing;
      const y = startY - row * spacing;
      const z = 0;

      // Create preview geometry (simple box for now)
      const geometry = new THREE.BoxGeometry(0.2, 0.2, 0.2);
      const material = new THREE.MeshStandardMaterial({
        color: 0x4CAF50,
        transparent: true,
        opacity: 0.8
      });
      
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(x, y, z);
      mesh.userData = { modelId: model.id, model };
      
      selectorGroup.add(mesh);

      // Store preview info
      const preview: ModelPreview = {
        mesh,
        model,
        originalPosition: new THREE.Vector3(x, y, z),
        isHovered: false,
        isSelected: model.id === selectedModel?.id
      };
      
      modelPreviewsRef.current.push(preview);

      // Add text label
      createTextLabel(model.name, mesh, selectorGroup);
    });
  }, [scene, models, position, selectedModel]);

  // Create text label for model
  const createTextLabel = useCallback((text: string, parentMesh: THREE.Mesh, group: THREE.Group) => {
    // Create canvas for text
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 64;
    const context = canvas.getContext('2d');
    
    if (!context) return;

    // Draw text
    context.fillStyle = 'rgba(0, 0, 0, 0.8)';
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    context.fillStyle = '#ffffff';
    context.font = '20px Arial';
    context.textAlign = 'center';
    context.fillText(text, canvas.width / 2, canvas.height / 2 + 7);

    // Create texture and material
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true
    });
    
    const geometry = new THREE.PlaneGeometry(0.25, 0.06);
    const labelMesh = new THREE.Mesh(geometry, material);
    
    // Position label below the model preview
    labelMesh.position.copy(parentMesh.position);
    labelMesh.position.y -= 0.15;
    
    group.add(labelMesh);
  }, []);

  // Handle controller interactions
  const handleControllerInteraction = useCallback((controller: THREE.Group) => {
    if (!selectorGroupRef.current || modelPreviewsRef.current.length === 0) return;

    // Create raycaster for controller
    const raycaster = new THREE.Raycaster();
    const tempMatrix = new THREE.Matrix4();
    
    // Get controller position and direction
    tempMatrix.identity().extractRotation(controller.matrixWorld);
    raycaster.ray.origin.setFromMatrixPosition(controller.matrixWorld);
    raycaster.ray.direction.set(0, 0, -1).applyMatrix4(tempMatrix);

    // Check intersections with model previews
    const meshes = modelPreviewsRef.current.map(preview => preview.mesh);
    const intersects = raycaster.intersectObjects(meshes);
    
    // Reset hover states
    modelPreviewsRef.current.forEach(preview => {
      if (preview.isHovered) {
        preview.isHovered = false;
        updatePreviewAppearance(preview);
      }
    });

    if (intersects.length > 0) {
      const intersectedMesh = intersects[0].object as THREE.Mesh;
      const preview = modelPreviewsRef.current.find(p => p.mesh === intersectedMesh);
      
      if (preview) {
        preview.isHovered = true;
        updatePreviewAppearance(preview);
        setHoveredModel(preview.model.id);
      }
    } else {
      setHoveredModel(null);
    }
  }, []);

  // Handle model selection
  const handleModelSelect = useCallback(async (model: AccessoryModel) => {
    try {
      setSelectedModel(model);
      
      // Load the model if not already loaded
      await loadModel(model);
      addLoadedModel(model.id);
      setModelVisibility(model.id, true);
      
      // Update preview appearances
      modelPreviewsRef.current.forEach(preview => {
        preview.isSelected = preview.model.id === model.id;
        updatePreviewAppearance(preview);
      });
      
      console.log(`Selected model: ${model.name}`);
    } catch (error) {
      console.error('Failed to select model:', error);
    }
  }, [setSelectedModel, loadModel, addLoadedModel, setModelVisibility]);

  // Update preview appearance based on state
  const updatePreviewAppearance = useCallback((preview: ModelPreview) => {
    const material = preview.mesh.material as THREE.MeshStandardMaterial;
    
    if (preview.isSelected) {
      material.color.setHex(0x4CAF50);
      material.opacity = 1.0;
      preview.mesh.scale.setScalar(1.2);
    } else if (preview.isHovered) {
      material.color.setHex(0x2196F3);
      material.opacity = 0.9;
      preview.mesh.scale.setScalar(1.1);
    } else {
      material.color.setHex(0x666666);
      material.opacity = 0.7;
      preview.mesh.scale.setScalar(1.0);
    }
  }, []);

  // Set up controller event listeners
  useEffect(() => {
    if (!isWebXRActive || controllers.length === 0) return;

    const handleSelectStart = (event: any) => {
      const controller = event.target;
      
      if (hoveredModel) {
        const preview = modelPreviewsRef.current.find(p => p.model.id === hoveredModel);
        if (preview) {
          handleModelSelect(preview.model);
        }
      }
    };

    const handleControllerMove = (event: any) => {
      const controller = event.target;
      handleControllerInteraction(controller);
    };

    controllers.forEach(controller => {
      controller.addEventListener('selectstart', handleSelectStart);
      // Note: 'move' event might not be available, using animation loop instead
    });

    return () => {
      controllers.forEach(controller => {
        controller.removeEventListener('selectstart', handleSelectStart);
      });
    };
  }, [isWebXRActive, controllers, hoveredModel, handleModelSelect, handleControllerInteraction]);

  // Continuous controller tracking
  useEffect(() => {
    if (!isWebXRActive || controllers.length === 0) return;

    const trackControllers = () => {
      controllers.forEach(controller => {
        handleControllerInteraction(controller);
      });
    };

    const interval = setInterval(trackControllers, 100); // Check every 100ms

    return () => {
      clearInterval(interval);
    };
  }, [isWebXRActive, controllers, handleControllerInteraction]);

  // Initialize model selector
  useEffect(() => {
    if (isWebXRActive && visible && models.length > 0 && !selectorGroupRef.current) {
      createModelPreviews();
    } else if (!isWebXRActive && selectorGroupRef.current) {
      // Clean up when exiting WebXR
      if (scene) {
        scene.remove(selectorGroupRef.current);
      }
      selectorGroupRef.current = null;
      modelPreviewsRef.current = [];
    }
  }, [isWebXRActive, visible, models, createModelPreviews, scene]);

  // Update selector when models change
  useEffect(() => {
    if (isWebXRActive && selectorGroupRef.current) {
      // Remove existing selector and recreate
      scene?.remove(selectorGroupRef.current);
      selectorGroupRef.current = null;
      modelPreviewsRef.current = [];
      
      if (models.length > 0) {
        createModelPreviews();
      }
    }
  }, [models, isWebXRActive, createModelPreviews, scene]);

  // Update selection states when selectedModel changes
  useEffect(() => {
    modelPreviewsRef.current.forEach(preview => {
      preview.isSelected = preview.model.id === selectedModel?.id;
      updatePreviewAppearance(preview);
    });
  }, [selectedModel, updatePreviewAppearance]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scene && selectorGroupRef.current) {
        scene.remove(selectorGroupRef.current);
      }
    };
  }, [scene]);

  // This component doesn't render anything to the DOM
  // All rendering is done in the Three.js scene
  return null;
};

export default WebXRModelSelector;
