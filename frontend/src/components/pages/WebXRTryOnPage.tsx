import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useQuery } from 'react-query';
import WebXRCamera from '@components/ar/WebXRCamera';
import WebXRUI from '@components/ar/WebXRUI';
import WebXRModelSelector from '@components/ar/WebXRModelSelector';
import { useWebXR } from '@hooks/useWebXR';
import { useWebXRFaceTracking } from '@hooks/useWebXRFaceTracking';
import { useAppStore } from '@stores/appStore';
import { apiService } from '@services/api';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  text-align: center;
  color: white;
`;

const Title = styled.h1`
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const Subtitle = styled.p`
  margin: 10px 0 0 0;
  font-size: 1.2rem;
  opacity: 0.9;
`;

const MainContent = styled.main`
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }
`;

const CameraSection = styled.section`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const ControlPanel = styled.aside`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const InfoCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  color: white;
`;

const InfoTitle = styled.h3`
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const InfoText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
  opacity: 0.9;
`;

const StatusIndicator = styled.div<{ $status: 'supported' | 'unsupported' | 'active' }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  background: ${props => {
    switch (props.$status) {
      case 'supported': return 'rgba(76, 175, 80, 0.2)';
      case 'active': return 'rgba(33, 150, 243, 0.2)';
      default: return 'rgba(244, 67, 54, 0.2)';
    }
  }};
  color: ${props => {
    switch (props.$status) {
      case 'supported': return '#4CAF50';
      case 'active': return '#2196F3';
      default: return '#f44336';
    }
  }};
`;

const FeatureList = styled.ul`
  margin: 12px 0 0 0;
  padding: 0;
  list-style: none;
`;

const FeatureItem = styled.li<{ $supported: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 0.85rem;
  color: ${props => props.$supported ? '#4CAF50' : '#f44336'};
`;

const WebXRTryOnPage: React.FC = () => {
  const [showAdvancedInfo, setShowAdvancedInfo] = useState(false);

  const {
    isSupported: isWebXRSupported,
    isSessionActive,
    sessionMode,
    capabilities,
    error: webXRError
  } = useWebXR();

  const {
    isTracking: isFaceTracking,
    trackingSource,
    isWebXRFaceTrackingSupported,
    error: trackingError
  } = useWebXRFaceTracking();

  const { selectedCategory, selectedModel } = useAppStore();

  // Fetch available models
  const { data: modelsData, isLoading: isLoadingModels } = useQuery(
    ['models', selectedCategory],
    () => apiService.getModelsList(),
    {
      staleTime: 5 * 60 * 1000,
      select: (data) => {
        if (!selectedCategory) return data?.models || [];
        return data?.models?.filter(model => model.category === selectedCategory) || [];
      }
    }
  );

  const getWebXRStatus = () => {
    if (webXRError) return 'unsupported';
    if (isSessionActive) return 'active';
    if (isWebXRSupported) return 'supported';
    return 'unsupported';
  };

  const getFaceTrackingStatus = () => {
    if (trackingError) return 'unsupported';
    if (isFaceTracking) return 'active';
    if (isWebXRFaceTrackingSupported) return 'supported';
    return 'unsupported';
  };

  return (
    <PageContainer>
      <Header>
        <Title>WebXR Virtual Try-On</Title>
        <Subtitle>Immersive AR/VR Experience for Virtual Accessories</Subtitle>
      </Header>

      <MainContent>
        <CameraSection>
          <WebXRCamera
            enableAR={capabilities.supportsAR}
            enableVR={capabilities.supportsVR}
            enableFallback={true}
          />
          
          {/* WebXR UI Components (only visible in WebXR mode) */}
          <WebXRUI visible={isSessionActive} />
          <WebXRModelSelector 
            visible={isSessionActive}
            models={modelsData || []}
          />
        </CameraSection>

        <ControlPanel>
          <InfoCard>
            <InfoTitle>WebXR Status</InfoTitle>
            <StatusIndicator $status={getWebXRStatus()}>
              <i className={`fas ${
                getWebXRStatus() === 'active' ? 'fa-vr-cardboard' :
                getWebXRStatus() === 'supported' ? 'fa-check-circle' : 'fa-times-circle'
              }`} />
              {isSessionActive ? `${sessionMode?.toUpperCase()} Active` :
               isWebXRSupported ? 'WebXR Supported' : 'WebXR Not Supported'}
            </StatusIndicator>
            
            {webXRError && (
              <InfoText style={{ color: '#f44336', marginTop: '8px' }}>
                Error: {webXRError}
              </InfoText>
            )}
          </InfoCard>

          <InfoCard>
            <InfoTitle>Face Tracking</InfoTitle>
            <StatusIndicator $status={getFaceTrackingStatus()}>
              <i className={`fas ${
                getFaceTrackingStatus() === 'active' ? 'fa-eye' :
                getFaceTrackingStatus() === 'supported' ? 'fa-check-circle' : 'fa-times-circle'
              }`} />
              {isFaceTracking ? `${trackingSource?.toUpperCase()} Tracking` :
               isWebXRFaceTrackingSupported ? 'Face Tracking Available' : 'Face Tracking Unavailable'}
            </StatusIndicator>
            
            {trackingError && (
              <InfoText style={{ color: '#f44336', marginTop: '8px' }}>
                Error: {trackingError}
              </InfoText>
            )}
          </InfoCard>

          <InfoCard>
            <InfoTitle>Supported Features</InfoTitle>
            <FeatureList>
              <FeatureItem $supported={capabilities.supportsAR}>
                <i className={`fas ${capabilities.supportsAR ? 'fa-check' : 'fa-times'}`} />
                Immersive AR
              </FeatureItem>
              <FeatureItem $supported={capabilities.supportsVR}>
                <i className={`fas ${capabilities.supportsVR ? 'fa-check' : 'fa-times'}`} />
                Immersive VR
              </FeatureItem>
              <FeatureItem $supported={capabilities.supportedFeatures.includes('hand-tracking')}>
                <i className={`fas ${capabilities.supportedFeatures.includes('hand-tracking') ? 'fa-check' : 'fa-times'}`} />
                Hand Tracking
              </FeatureItem>
              <FeatureItem $supported={capabilities.supportedFeatures.includes('face-tracking')}>
                <i className={`fas ${capabilities.supportedFeatures.includes('face-tracking') ? 'fa-check' : 'fa-times'}`} />
                Face Tracking
              </FeatureItem>
              <FeatureItem $supported={capabilities.supportedFeatures.includes('hit-test')}>
                <i className={`fas ${capabilities.supportedFeatures.includes('hit-test') ? 'fa-check' : 'fa-times'}`} />
                Hit Testing
              </FeatureItem>
            </FeatureList>
          </InfoCard>

          <InfoCard>
            <InfoTitle>Current Selection</InfoTitle>
            <InfoText>
              <strong>Category:</strong> {selectedCategory || 'None'}
            </InfoText>
            <InfoText style={{ marginTop: '8px' }}>
              <strong>Model:</strong> {selectedModel?.name || 'None'}
            </InfoText>
            <InfoText style={{ marginTop: '8px' }}>
              <strong>Available Models:</strong> {modelsData?.length || 0}
            </InfoText>
          </InfoCard>

          <InfoCard>
            <InfoTitle>Instructions</InfoTitle>
            <InfoText>
              <strong>Desktop/Mobile:</strong> Use the camera view and on-screen controls to try on accessories.
            </InfoText>
            <InfoText style={{ marginTop: '8px' }}>
              <strong>WebXR AR:</strong> Point your device at your face and use hand gestures or controllers to select models.
            </InfoText>
            <InfoText style={{ marginTop: '8px' }}>
              <strong>WebXR VR:</strong> Use controllers to navigate the virtual environment and select accessories.
            </InfoText>
          </InfoCard>

          {showAdvancedInfo && (
            <InfoCard>
              <InfoTitle>Advanced Information</InfoTitle>
              <InfoText>
                <strong>Supported Features:</strong> {capabilities.supportedFeatures.join(', ') || 'None'}
              </InfoText>
              <InfoText style={{ marginTop: '8px' }}>
                <strong>Session Mode:</strong> {sessionMode || 'None'}
              </InfoText>
              <InfoText style={{ marginTop: '8px' }}>
                <strong>Tracking Source:</strong> {trackingSource}
              </InfoText>
            </InfoCard>
          )}

          <button
            onClick={() => setShowAdvancedInfo(!showAdvancedInfo)}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '8px',
              color: 'white',
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            {showAdvancedInfo ? 'Hide' : 'Show'} Advanced Info
          </button>
        </ControlPanel>
      </MainContent>
    </PageContainer>
  );
};

export default WebXRTryOnPage;
