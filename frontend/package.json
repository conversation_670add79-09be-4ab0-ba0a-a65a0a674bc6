{"name": "virtual-try-on-frontend", "version": "1.0.0", "description": "Virtual Try-On AR System Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@types/three": "^0.158.0", "@webxr-input-profiles/motion-controllers": "^1.0.0", "axios": "^1.6.0", "framer-motion": "^10.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.0", "react-router-dom": "^6.8.0", "styled-components": "^6.1.0", "three": "^0.158.0", "three-mesh-ui": "^6.5.4", "zustand": "^4.4.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.2", "vite": "^4.4.0"}, "engines": {"node": ">=18.0.0"}}