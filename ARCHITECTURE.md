# Virtual Try-On Architecture

## 🏗️ New Architecture Overview

The Virtual Try-On system has been successfully separated into a modern **Node.js frontend** and **Django API backend** architecture.

```
┌─────────────────────┐    HTTP/API     ┌─────────────────────┐
│                     │    Requests     │                     │
│   React Frontend    │◄───────────────►│   Django Backend    │
│   (Node.js/Vite)    │                 │   (API Only)        │
│   Port: 3000        │                 │   Port: 8000        │
│                     │                 │                     │
└─────────────────────┘                 └─────────────────────┘
```

## 🎯 Architecture Benefits

### Frontend (React + TypeScript)
- **Modern Development**: React 18 with TypeScript for type safety
- **Fast Development**: Vite for instant HMR and fast builds
- **Component-Based**: Reusable UI components with styled-components
- **State Management**: Zustand for client state, React Query for server state
- **3D Graphics**: Three.js integration for AR rendering
- **Real-time Features**: MediaPipe for client-side face detection

### Backend (Django API)
- **API-First**: RESTful API design with Django REST Framework
- **Scalable**: Separated concerns allow independent scaling
- **Robust**: Existing face detection and model management logic preserved
- **Secure**: CORS configuration, CSRF protection, file upload validation
- **Database**: PostgreSQL for production, SQLite for development

## 📁 Project Structure

```
virtual-try-on/
├── frontend/                    # React.js Frontend
│   ├── src/
│   │   ├── components/         # React components
│   │   ├── services/           # API client
│   │   ├── types/              # TypeScript definitions
│   │   ├── hooks/              # Custom React hooks
│   │   └── stores/             # State management
│   ├── package.json
│   └── vite.config.ts
├── face_detection/             # Django Face Detection App
├── models_manager/             # Django Model Management App
├── virtual_try_on/             # Django Settings
├── static/                     # Legacy static files (to be migrated)
├── templates/                  # Legacy templates (deprecated)
├── manage.py                   # Django management
├── start-dev.sh               # Development startup script
└── README.md
```

## 🚀 Development Setup

### Quick Start

1. **Start both servers**:
   ```bash
   ./start-dev.sh
   ```

2. **Manual setup**:
   ```bash
   # Terminal 1: Django Backend
   python manage.py runserver 8000
   
   # Terminal 2: React Frontend
   cd frontend && npm run dev
   ```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api/
   - Django Admin: http://localhost:8000/admin/

### Prerequisites

- Python 3.12+
- Node.js 18+
- npm or pnpm

## 🔌 API Integration

### Proxy Configuration

The React development server proxies API requests to Django:

```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': 'http://localhost:8000',
    '/media': 'http://localhost:8000'
  }
}
```

### API Client

Centralized API client with TypeScript support:

```typescript
// src/services/api.ts
import { apiService } from '@services/api';

// Face detection
const result = await apiService.detectFace(imageBlob);

// Model management
const models = await apiService.getModelsList();
```

## 🔧 Configuration

### Django Settings

Key changes for API-only backend:

```python
# CORS for React frontend
CORS_ALLOWED_ORIGINS = ["http://localhost:3000"]

# REST Framework configuration
REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': ['rest_framework.renderers.JSONRenderer'],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.MultiPartParser',
    ],
}

# Session and CSRF for API usage
CSRF_COOKIE_HTTPONLY = False
SESSION_COOKIE_SAMESITE = 'Lax'
```

### Environment Variables

```bash
# .env
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3000
```

## 📊 API Endpoints

### Face Detection
- `POST /api/face/detect/` - Detect face landmarks
- `GET /api/face/history/` - Get detection history
- `POST /api/face/clear-session/` - Clear session

### Model Management
- `GET /api/models/list/` - List models by category
- `POST /api/models/models/` - Upload new model
- `GET /api/models/categories/` - List categories

### Health Check
- `GET /api/health/` - Backend health status

## 🔄 Migration Status

### ✅ Completed
- [x] React frontend project setup
- [x] Django API-only configuration
- [x] Development environment setup
- [x] API client implementation
- [x] Basic UI components
- [x] Proxy configuration

### 🚧 In Progress
- [ ] Migrate AR components to React
- [ ] Implement face tracking hooks
- [ ] Create 3D rendering components
- [ ] Add state management
- [ ] Migrate existing JavaScript modules

### 📋 Next Steps
1. Port face tracking logic to React hooks
2. Create Three.js React components
3. Implement model selection UI
4. Add photo capture functionality
5. Set up testing framework

## 🧪 Testing

### Backend Testing
```bash
# Test API endpoints
curl http://localhost:8000/api/health/
curl http://localhost:8000/api/models/list/
```

### Frontend Testing
```bash
cd frontend
npm run lint          # ESLint
npm run type-check     # TypeScript
npm run build          # Production build
```

## 🚀 Deployment

### Production Build
```bash
# Frontend
cd frontend && npm run build

# Backend
python manage.py collectstatic
python manage.py migrate
```

### Docker Support (Future)
```dockerfile
# Multi-stage build for frontend + Django backend
FROM node:18 AS frontend-build
# ... build React app

FROM python:3.12 AS backend
# ... copy built frontend and Django app
```

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**: Check `CORS_ALLOWED_ORIGINS` in Django settings
2. **API 404s**: Ensure Django is running on port 8000
3. **Build Errors**: Verify Node.js and npm versions
4. **Import Errors**: Check TypeScript path aliases in `tsconfig.json`

### Debug Mode

Enable detailed logging:
```python
# Django settings.py
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {'class': 'logging.StreamHandler'},
    },
    'root': {'handlers': ['console'], 'level': 'DEBUG'},
}
```

## 📈 Performance

### Optimization Features

- **Code Splitting**: Vite automatically splits vendor and app code
- **Tree Shaking**: Unused code elimination
- **API Caching**: React Query for intelligent data caching
- **Image Optimization**: WebP support for model thumbnails
- **Lazy Loading**: Components loaded on demand

### Monitoring

- Frontend: React DevTools, Vite build analyzer
- Backend: Django Debug Toolbar, API response times
- Network: Browser DevTools for API performance
